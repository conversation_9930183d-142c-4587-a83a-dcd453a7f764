import { Link } from "@remix-run/react";

interface MetricsProps {
  totalBookings: number;
  pendingJobs: number;
  completedJobs: number;
  activeClients: number;
}

export function DashboardMetrics({ 
  totalBookings, 
  pendingJobs, 
  completedJobs, 
  activeClients 
}: MetricsProps) {
  const metrics = [
    {
      name: "Total Bookings",
      value: totalBookings,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: "bg-blue-100 text-blue-800",
      link: "#bookings"
    },
    {
      name: "Pending Jobs",
      value: pendingJobs,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: "bg-yellow-100 text-yellow-800",
      link: "#bookings?status=pending"
    },
    {
      name: "Completed Jobs",
      value: completedJobs,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      ),
      color: "bg-green-100 text-green-800",
      link: "#bookings?status=completed"
    },
    {
      name: "Active Clients",
      value: activeClients,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      ),
      color: "bg-purple-100 text-purple-800",
      link: "#clients"
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {metrics.map((metric) => (
        <Link 
          key={metric.name}
          to={metric.link}
          className="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <div className={`${metric.color} p-3 rounded-full mr-4`}>
              {metric.icon}
            </div>
            <div>
              <p className="text-sm text-gray-500">{metric.name}</p>
              <p className="text-2xl font-semibold">{metric.value}</p>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
}
