import { forwardRef } from "react";
import { twMerge } from "tailwind-merge";

/* 
 * MOBILE-FIRST FORM COMPONENTS
 * 
 * Optimized for touch interaction and mobile usability
 * Key features:
 * - Large touch targets (min 44px height)
 * - Clear labels and validation feedback
 * - Mobile-friendly input types
 * - Proper autocomplete attributes
 * - Touch-friendly spacing and typography
 */

interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  helpText?: string;
  children: React.ReactNode;
}

export function FormField({ label, error, required, helpText, children }: FormFieldProps) {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children}
      {helpText && !error && (
        <p className="text-xs text-gray-500">{helpText}</p>
      )}
      {error && (
        <div className="flex items-start space-x-2">
          <svg className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-xs text-red-600">{error}</p>
        </div>
      )}
    </div>
  );
}

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, error, ...props }, ref) => {
    return (
      <input
        ref={ref}
        className={twMerge(
          // Base styles - mobile-first with large touch targets
          "w-full px-4 py-3 text-base border rounded-lg transition-colors",
          "placeholder-gray-400 bg-white",
          // Focus states
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          // Error states
          error 
            ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
            : "border-gray-300",
          // Disabled states
          "disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed",
          className
        )}
        {...props}
      />
    );
  }
);

Input.displayName = "Input";

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
}

export const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  ({ className, error, ...props }, ref) => {
    return (
      <textarea
        ref={ref}
        className={twMerge(
          "w-full px-4 py-3 text-base border rounded-lg transition-colors resize-none",
          "placeholder-gray-400 bg-white min-h-[100px]",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          error 
            ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
            : "border-gray-300",
          "disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed",
          className
        )}
        {...props}
      />
    );
  }
);

TextArea.displayName = "TextArea";

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  placeholder?: string;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, error, placeholder, children, ...props }, ref) => {
    return (
      <div className="relative">
        <select
          ref={ref}
          className={twMerge(
            "w-full px-4 py-3 text-base border rounded-lg transition-colors appearance-none bg-white",
            "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
            error 
              ? "border-red-300 focus:ring-red-500 focus:border-red-500" 
              : "border-gray-300",
            "disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed",
            className
          )}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {children}
        </select>
        {/* Custom arrow */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>
    );
  }
);

Select.displayName = "Select";

interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  description?: string;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, ...props }, ref) => {
    return (
      <label className="flex items-start space-x-3 cursor-pointer touch-manipulation">
        <input
          ref={ref}
          type="checkbox"
          className={twMerge(
            "mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",
            className
          )}
          {...props}
        />
        <div className="flex-1">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          {description && (
            <p className="text-xs text-gray-500 mt-0.5">{description}</p>
          )}
        </div>
      </label>
    );
  }
);

Checkbox.displayName = "Checkbox";

interface RadioGroupProps {
  name: string;
  options: Array<{ value: string; label: string; description?: string }>;
  value?: string;
  onChange?: (value: string) => void;
  error?: boolean;
}

export function RadioGroup({ name, options, value, onChange, error }: RadioGroupProps) {
  return (
    <div className="space-y-3">
      {options.map((option) => (
        <label 
          key={option.value} 
          className="flex items-start space-x-3 cursor-pointer touch-manipulation"
        >
          <input
            type="radio"
            name={name}
            value={option.value}
            checked={value === option.value}
            onChange={(e) => onChange?.(e.target.value)}
            className={twMerge(
              "mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500",
              error ? "border-red-300" : ""
            )}
          />
          <div className="flex-1">
            <span className="text-sm font-medium text-gray-700">{option.label}</span>
            {option.description && (
              <p className="text-xs text-gray-500 mt-0.5">{option.description}</p>
            )}
          </div>
        </label>
      ))}
    </div>
  );
}

/* MOBILE-SPECIFIC INPUT TYPES */

// Phone input with proper mobile keyboard
export const PhoneInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => (
    <Input 
      ref={ref}
      type="tel" 
      autoComplete="tel" 
      placeholder="(*************"
      {...props} 
    />
  )
);

PhoneInput.displayName = "PhoneInput";

// Email input with mobile optimization
export const EmailInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => (
    <Input 
      ref={ref}
      type="email" 
      autoComplete="email" 
      placeholder="<EMAIL>"
      {...props} 
    />
  )
);

EmailInput.displayName = "EmailInput";

// Date input with mobile date picker
export const DateInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => (
    <Input 
      ref={ref}
      type="date" 
      {...props} 
    />
  )
);

DateInput.displayName = "DateInput";

// Time input with mobile time picker
export const TimeInput = forwardRef<HTMLInputElement, Omit<InputProps, 'type'>>(
  (props, ref) => (
    <Input 
      ref={ref}
      type="time" 
      {...props} 
    />
  )
);

TimeInput.displayName = "TimeInput";

// File input optimized for mobile (camera integration)
interface FileInputProps extends Omit<InputProps, 'type'> {
  accept?: string;
  capture?: boolean; // Enable camera capture on mobile
}

export const FileInput = forwardRef<HTMLInputElement, FileInputProps>(
  ({ capture, accept = "image/*", className, ...props }, ref) => (
    <Input 
      ref={ref}
      type="file" 
      accept={accept}
      capture={capture ? "environment" : undefined} // Use rear camera by default
      className={twMerge("py-2", className)}
      {...props} 
    />
  )
);

FileInput.displayName = "FileInput";
