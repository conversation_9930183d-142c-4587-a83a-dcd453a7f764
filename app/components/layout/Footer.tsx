import { Link } from "@remix-run/react";
import { Container } from "~/components/ui/Container";

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-8">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Sia Moon Property Care</h3>
            <p className="text-gray-400 mb-2">Email: <EMAIL></p>
            <p className="text-gray-400">Phone: +****************</p>
          </div>
          <div className="md:text-right">
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul>
              <li className="mb-2">
                <Link to="/services" className="text-gray-400 hover:text-white transition-colors">
                  Services
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/about" className="text-gray-400 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-white transition-colors">
                  Terms & Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-6 text-center text-gray-500 text-sm">
          <p>&copy; {new Date().getFullYear()} Sia Moon Property Care. All rights reserved.</p>
        </div>
      </Container>
    </footer>
  );
}
