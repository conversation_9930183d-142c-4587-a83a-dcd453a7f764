import { Link } from "@remix-run/react";
import { Container } from "~/components/ui/Container";

interface MobileLayoutProps {
  children: React.ReactNode;
  showNav?: boolean;
}

export function MobileLayout({ children, showNav = true }: MobileLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {showNav && (
        <header className="bg-white shadow-sm sticky top-0 z-10">
          <Container>
            <div className="flex items-center justify-between py-4">
              <Link to="/" className="text-xl font-bold text-blue-600">
                Sia Moon
              </Link>
              <nav className="flex items-center space-x-4">
                <Link to="/properties" className="text-gray-600 hover:text-blue-600">
                  Properties
                </Link>
                <Link to="/profile" className="text-gray-600 hover:text-blue-600">
                  Profile
                </Link>
              </nav>
            </div>
          </Container>
        </header>
      )}
      
      <main className="flex-grow">
        {children}
      </main>
      
      <footer className="bg-gray-50 py-6 mt-auto">
        <Container>
          <div className="text-center text-gray-500 text-sm">
            <p>&copy; {new Date().getFullYear()} Sia Moon Property Care. All rights reserved.</p>
          </div>
        </Container>
      </footer>
    </div>
  );
}
