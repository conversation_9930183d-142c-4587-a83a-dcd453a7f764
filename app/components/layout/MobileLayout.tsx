import { useState, useEffect } from "react";
import { Header } from "./Header";
import { Footer } from "./Footer";
import { MobileNavBar } from "./MobileNavBar";

interface MobileLayoutProps {
  children: React.ReactNode;
  user?: {
    id: string;
    email: string;
    role?: string;
  } | null;
  hideNavBar?: boolean;
  hideFooter?: boolean;
}

export function MobileLayout({
  children,
  user,
  hideNavBar = false,
  hideFooter = false
}: MobileLayoutProps) {
  const [isScrollingUp, setIsScrollingUp] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Handle scroll direction detection for hiding/showing bottom nav
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY < lastScrollY || currentScrollY < 50) {
        setIsScrollingUp(true);
      } else if (currentScrollY > lastScrollY && currentScrollY > 50) {
        setIsScrollingUp(false);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  return (
    <div className="flex min-h-screen flex-col">
      <Header user={user} />

      {/* Main content with padding to account for bottom nav */}
      <main className="flex-1 pb-16">
        {children}
      </main>

      {!hideFooter && <Footer />}

      {/* Bottom Navigation - only show if not hidden and user is authenticated */}
      {!hideNavBar && user && (
        <div
          className={`fixed bottom-0 left-0 right-0 z-50 transition-transform duration-300 ${
            isScrollingUp ? 'translate-y-0' : 'translate-y-full'
          }`}
        >
          <MobileNavBar user={user} />
        </div>
      )}
    </div>
  );
}
