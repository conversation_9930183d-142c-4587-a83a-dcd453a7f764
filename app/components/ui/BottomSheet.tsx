import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { twMerge } from "tailwind-merge";

/* 
 * MOBILE BOTTOM SHEET MODAL
 * 
 * Perfect for mobile interfaces - slides up from bottom
 * Key features:
 * - Touch gestures for dismissing
 * - Proper backdrop handling
 * - Responsive height options
 * - Smooth animations
 * - Accessibility support
 */

interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: "small" | "medium" | "large" | "full";
  className?: string;
  preventClose?: boolean; // Prevent closing by backdrop click or swipe
}

export function BottomSheet({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = "medium",
  className,
  preventClose = false
}: BottomSheetProps) {
  // const [isAnimating, setIsAnimating] = useState(false); // TODO: Implement animations
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  // Handle sheet height based on size
  const getSheetHeight = () => {
    switch (size) {
      case "small": return "max-h-[40vh]";
      case "medium": return "max-h-[60vh]";
      case "large": return "max-h-[80vh]";
      case "full": return "max-h-[90vh]";
      default: return "max-h-[60vh]";
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && !preventClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose, preventClose]);

  // Touch handlers for swipe-to-close
  const handleTouchStart = (e: React.TouchEvent) => {
    if (preventClose) return;
    setStartY(e.touches[0].clientY);
    setIsDragging(true);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (preventClose || !isDragging) return;
    
    const touchY = e.touches[0].clientY;
    const diff = touchY - startY;
    
    // Only allow downward swipes
    if (diff > 0) {
      setCurrentY(diff);
    }
  };

  const handleTouchEnd = () => {
    if (preventClose) return;
    
    setIsDragging(false);
    
    // If swiped down more than 100px, close the sheet
    if (currentY > 100) {
      onClose();
    }
    
    setCurrentY(0);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !preventClose) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const content = (
    <div
      className="fixed inset-0 z-50 flex items-end justify-center"
      onClick={handleBackdropClick}
      onKeyDown={(e) => e.key === 'Escape' && onClose()}
      role="button"
      aria-label="Close dialog"
      tabIndex={0}
    >
      {/* Backdrop */}
      <div 
        className={twMerge(
          "absolute inset-0 bg-black transition-opacity duration-300",
          isOpen ? "opacity-50" : "opacity-0"
        )}
      />
      
      {/* Bottom Sheet */}
      <div 
        className={twMerge(
          "relative w-full bg-white rounded-t-xl shadow-xl transition-transform duration-300 ease-out",
          getSheetHeight(),
          isOpen ? "translate-y-0" : "translate-y-full",
          className
        )}
        style={{
          transform: `translateY(${isDragging ? currentY : 0}px)`
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Handle bar for visual drag indicator */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full" />
        </div>
        
        {/* Header */}
        {title && (
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {!preventClose && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors touch-manipulation"
                aria-label="Close"
              >
                <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}
        
        {/* Content */}
        <div className="overflow-y-auto overscroll-contain px-4 py-4 pb-8">
          {children}
        </div>
      </div>
    </div>
  );

  // Render in portal to ensure proper layering
  return createPortal(content, document.body);
}

/* 
 * MOBILE ACTION SHEET COMPONENT
 * 
 * Common mobile pattern for presenting action options
 */

interface ActionSheetOption {
  label: string;
  onClick: () => void;
  variant?: "default" | "destructive";
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface ActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  options: ActionSheetOption[];
  cancelLabel?: string;
}

export function ActionSheet({ 
  isOpen, 
  onClose, 
  title, 
  options, 
  cancelLabel = "Cancel" 
}: ActionSheetProps) {
  return (
    <BottomSheet isOpen={isOpen} onClose={onClose} size="small">
      <div className="space-y-2">
        {title && (
          <div className="text-center pb-2">
            <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          </div>
        )}
        
        {/* Action options */}
        <div className="space-y-1">
          {options.map((option, index) => (
            <button
              key={index}
              onClick={() => {
                option.onClick();
                onClose();
              }}
              disabled={option.disabled}
              className={twMerge(
                "w-full flex items-center justify-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-colors touch-manipulation",
                option.variant === "destructive" 
                  ? "text-red-600 hover:bg-red-50" 
                  : "text-gray-900 hover:bg-gray-50",
                option.disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {option.icon && <span className="flex-shrink-0">{option.icon}</span>}
              <span>{option.label}</span>
            </button>
          ))}
        </div>
        
        {/* Cancel button */}
        <div className="pt-2 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full py-3 text-base font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors touch-manipulation"
          >
            {cancelLabel}
          </button>
        </div>
      </div>
    </BottomSheet>
  );
}

/* 
 * MOBILE CONFIRMATION DIALOG
 * 
 * Mobile-optimized confirmation dialog
 */

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: "default" | "destructive";
}

export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  variant = "default"
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <BottomSheet isOpen={isOpen} onClose={onClose} size="small" preventClose>
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
          <p className="text-gray-600 text-sm leading-relaxed">{message}</p>
        </div>
        
        <div className="space-y-3 pt-2">
          <button
            onClick={handleConfirm}
            className={twMerge(
              "w-full py-3 px-4 rounded-lg text-base font-medium transition-colors touch-manipulation",
              variant === "destructive"
                ? "bg-red-600 text-white hover:bg-red-700"
                : "bg-blue-600 text-white hover:bg-blue-700"
            )}
          >
            {confirmLabel}
          </button>
          
          <button
            onClick={onClose}
            className="w-full py-3 px-4 text-base font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors touch-manipulation"
          >
            {cancelLabel}
          </button>
        </div>
      </div>
    </BottomSheet>
  );
}
