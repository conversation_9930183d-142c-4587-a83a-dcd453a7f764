import { Link } from "@remix-run/react";
import { forwardRef } from "react";
import { twMerge } from "tailwind-merge";

type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "danger" | "gradient" | "glass";
type ButtonSize = "sm" | "md" | "lg" | "xl";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
  glow?: boolean;
}

interface ButtonLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  to: string;
  children: React.ReactNode;
  glow?: boolean;
}

const variantStyles: Record<ButtonVariant, string> = {
  primary: "bg-accent-500 hover:bg-accent-600 active:bg-accent-700 text-surface-primary shadow-button hover:shadow-elevated focus:ring-accent-500/20 font-medium",
  secondary: "bg-surface-quaternary hover:bg-interactive-hover active:bg-interactive-active text-text-primary border border-border-primary shadow-button hover:shadow-elevated focus:ring-border-primary/20",
  outline: "border border-border-primary bg-transparent hover:bg-interactive-hover text-text-primary shadow-button hover:shadow-elevated focus:ring-border-primary/20",
  ghost: "bg-transparent hover:bg-interactive-hover text-text-secondary hover:text-text-primary focus:ring-border-primary/20",
  danger: "bg-status-error hover:bg-red-600 active:bg-red-700 text-white shadow-button hover:shadow-elevated focus:ring-red-500/20",
  gradient: "bg-gradient-accent hover:bg-gradient-accent-hover text-surface-primary shadow-button hover:shadow-elevated focus:ring-accent-500/20 font-medium border border-accent-600",
  glass: "bg-surface-secondary/50 backdrop-blur-md border border-border-primary/50 text-text-primary hover:bg-surface-tertiary/50 shadow-card hover:shadow-elevated focus:ring-border-primary/20",
};

const sizeStyles: Record<ButtonSize, string> = {
  sm: "text-xs px-3 py-2 rounded-lg min-h-[32px]",
  md: "text-sm px-4 py-2.5 rounded-xl min-h-[40px]",
  lg: "text-base px-6 py-3 rounded-xl min-h-[44px]",
  xl: "text-lg px-8 py-4 rounded-2xl min-h-[52px]",
};

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    variant = "primary",
    size = "md",
    isLoading = false,
    fullWidth = false,
    glow = false,
    className = "",
    disabled,
    children,
    ...props
  }, ref) => {
    const glowClass = glow ? "hover:shadow-medium" : "";

    return (
      <button
        ref={ref}
        disabled={disabled || isLoading}
        className={twMerge(
          "btn-base touch-target font-medium transition-all duration-200 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface-primary disabled:opacity-50 disabled:pointer-events-none transform hover:scale-[1.01] active:scale-[0.99]",
          variantStyles[variant],
          sizeStyles[size],
          fullWidth ? "w-full" : "",
          glowClass,
          isLoading ? "cursor-not-allowed" : "",
          className
        )}
        {...props}
      >
        {isLoading && (
          <div className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="animate-pulse">Loading...</span>
          </div>
        )}
        {!isLoading && children}
      </button>
    );
  }
);

Button.displayName = "Button";

export const ButtonLink = forwardRef<HTMLAnchorElement, ButtonLinkProps>(
  ({
    variant = "primary",
    size = "md",
    fullWidth = false,
    glow = false,
    className = "",
    to,
    children,
    ...props
  }, ref) => {
    const glowClass = glow ? "hover:shadow-medium" : "";

    return (
      <Link
        ref={ref}
        to={to}
        className={twMerge(
          "btn-base touch-target font-medium transition-all duration-200 ease-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface-primary transform hover:scale-[1.01] active:scale-[0.99]",
          variantStyles[variant],
          sizeStyles[size],
          fullWidth ? "w-full" : "",
          glowClass,
          className
        )}
        {...props}
      >
        {children}
      </Link>
    );
  }
);

ButtonLink.displayName = "ButtonLink";
