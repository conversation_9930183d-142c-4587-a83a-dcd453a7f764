import { Link } from "@remix-run/react";
import { forwardRef } from "react";
import { twMerge } from "tailwind-merge";

type ButtonVariant = "primary" | "secondary" | "outline" | "ghost" | "danger" | "gradient" | "glass";
type ButtonSize = "sm" | "md" | "lg" | "xl";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
  glow?: boolean;
}

interface ButtonLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  to: string;
  children: React.ReactNode;
  glow?: boolean;
}

const variantStyles: Record<ButtonVariant, string> = {
  primary: "bg-gradient-to-r from-secondary-700 to-secondary-800 hover:from-secondary-600 hover:to-secondary-700 text-neutral-100 shadow-soft hover:shadow-medium focus:ring-accent-500 border-0",
  secondary: "bg-gradient-to-r from-secondary-800 to-secondary-900 hover:from-secondary-700 hover:to-secondary-800 text-neutral-100 shadow-soft hover:shadow-medium focus:ring-accent-500 border-0",
  outline: "bg-secondary-900/80 backdrop-blur-sm border border-secondary-600 text-neutral-100 hover:bg-secondary-800/80 hover:border-accent-500 focus:ring-accent-500 shadow-soft hover:shadow-medium",
  ghost: "bg-transparent text-neutral-100 hover:bg-secondary-800/50 focus:ring-accent-500",
  danger: "bg-gradient-to-r from-error-500 to-error-600 hover:from-error-600 hover:to-error-700 text-white shadow-soft hover:shadow-medium focus:ring-error-500 border-0",
  gradient: "bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-400 hover:to-accent-500 text-black shadow-glow-yellow hover:shadow-glow-yellow focus:ring-accent-500 border-0 font-bold",
  glass: "bg-secondary-800/20 backdrop-blur-md border border-secondary-600/30 text-neutral-100 hover:bg-secondary-700/30 hover:border-accent-500/50 focus:ring-accent-500 shadow-soft hover:shadow-medium",
};

const sizeStyles: Record<ButtonSize, string> = {
  sm: "text-xs px-3 py-2 rounded-lg min-h-[32px]",
  md: "text-sm px-4 py-2.5 rounded-xl min-h-[40px]",
  lg: "text-base px-6 py-3 rounded-xl min-h-[44px]",
  xl: "text-lg px-8 py-4 rounded-2xl min-h-[52px]",
};

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    variant = "primary",
    size = "md",
    isLoading = false,
    fullWidth = false,
    glow = false,
    className = "",
    disabled,
    children,
    ...props
  }, ref) => {
    const glowClass = glow ? "hover:shadow-glow" : "";

    return (
      <button
        ref={ref}
        disabled={disabled || isLoading}
        className={twMerge(
          "btn-base touch-target font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black disabled:opacity-50 disabled:pointer-events-none transform hover:scale-[1.02] active:scale-[0.98]",
          variantStyles[variant],
          sizeStyles[size],
          fullWidth ? "w-full" : "",
          glowClass,
          isLoading ? "cursor-not-allowed" : "",
          className
        )}
        {...props}
      >
        {isLoading && (
          <div className="flex items-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="animate-pulse">Loading...</span>
          </div>
        )}
        {!isLoading && children}
      </button>
    );
  }
);

Button.displayName = "Button";

export const ButtonLink = forwardRef<HTMLAnchorElement, ButtonLinkProps>(
  ({
    variant = "primary",
    size = "md",
    fullWidth = false,
    glow = false,
    className = "",
    to,
    children,
    ...props
  }, ref) => {
    const glowClass = glow ? "hover:shadow-glow" : "";

    return (
      <Link
        ref={ref}
        to={to}
        className={twMerge(
          "btn-base touch-target font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02] active:scale-[0.98]",
          variantStyles[variant],
          sizeStyles[size],
          fullWidth ? "w-full" : "",
          glowClass,
          className
        )}
        {...props}
      >
        {children}
      </Link>
    );
  }
);

ButtonLink.displayName = "ButtonLink";
