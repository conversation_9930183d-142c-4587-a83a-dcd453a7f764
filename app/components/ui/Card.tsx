import { twMerge } from "tailwind-merge";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "glass" | "gradient" | "outline";
  padding?: "none" | "sm" | "md" | "lg";
  hover?: boolean;
  as?: React.ElementType;
  style?: React.CSSProperties;
}

const variantStyles = {
  default: "bg-secondary-900 border border-secondary-700",
  glass: "glass",
  gradient: "bg-gradient-to-br from-secondary-900 to-black border border-secondary-700/50",
  outline: "bg-transparent border border-secondary-600",
};

const paddingStyles = {
  none: "",
  sm: "p-4",
  md: "p-6",
  lg: "p-8",
};

export function Card({
  children,
  className = "",
  variant = "default",
  padding = "md",
  hover = false,
  as: Component = "div",
  style,
  ...props
}: CardProps) {
  return (
    <Component
      className={twMerge(
        "rounded-2xl shadow-soft transition-all duration-300",
        variantStyles[variant],
        paddingStyles[padding],
        hover ? "hover:shadow-medium hover:scale-[1.02] cursor-pointer" : "",
        className
      )}
      style={style}
      {...props}
    >
      {children}
    </Component>
  );
}

// Specialized card components
export function PropertyCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="gradient" 
      hover 
      className={twMerge("group", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}

export function DashboardCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="glass" 
      hover 
      className={twMerge("backdrop-blur-md", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}

export function StatsCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="gradient" 
      className={twMerge("text-center", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}
