import { twMerge } from "tailwind-merge";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "glass" | "gradient" | "outline";
  padding?: "none" | "sm" | "md" | "lg";
  hover?: boolean;
  as?: React.ElementType;
  style?: React.CSSProperties;
}

const variantStyles = {
  default: "bg-surface-secondary border border-border-primary",
  glass: "bg-surface-secondary/50 backdrop-blur-md border border-border-primary/50",
  gradient: "bg-gradient-surface border border-border-primary",
  outline: "bg-transparent border border-border-primary",
};

const paddingStyles = {
  none: "",
  sm: "p-4",
  md: "p-6",
  lg: "p-8",
};

export function Card({
  children,
  className = "",
  variant = "default",
  padding = "md",
  hover = false,
  as: Component = "div",
  style,
  ...props
}: CardProps) {
  return (
    <Component
      className={twMerge(
        "rounded-xl shadow-card transition-all duration-200 ease-out",
        variantStyles[variant],
        paddingStyles[padding],
        hover ? "hover:shadow-elevated hover:scale-[1.01] cursor-pointer" : "",
        className
      )}
      style={style}
      {...props}
    >
      {children}
    </Component>
  );
}

// Specialized card components
export function PropertyCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="gradient" 
      hover 
      className={twMerge("group", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}

export function DashboardCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="glass" 
      hover 
      className={twMerge("backdrop-blur-md", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}

export function StatsCard({ 
  children, 
  className = "", 
  ...props 
}: Omit<CardProps, 'variant'>) {
  return (
    <Card 
      variant="gradient" 
      className={twMerge("text-center", className)} 
      {...props}
    >
      {children}
    </Card>
  );
}
