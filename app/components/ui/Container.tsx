import { twMerge } from "tailwind-merge";

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
}

export function Container({ 
  children, 
  className = "", 
  as: Component = "div" 
}: ContainerProps) {
  return (
    <Component className={twMerge("mx-auto w-full px-4 sm:px-6 lg:px-8 max-w-7xl", className)}>
      {children}
    </Component>
  );
}
