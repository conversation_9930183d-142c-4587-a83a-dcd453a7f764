import { twMerge } from "tailwind-merge";

interface ContainerProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
  size?: "sm" | "md" | "lg" | "xl" | "full";
  padding?: "none" | "sm" | "md" | "lg";
}

const sizeStyles = {
  sm: "max-w-2xl",
  md: "max-w-4xl",
  lg: "max-w-6xl",
  xl: "max-w-7xl",
  full: "max-w-none",
};

const paddingStyles = {
  none: "",
  sm: "px-4 sm:px-6",
  md: "px-4 sm:px-6 lg:px-8",
  lg: "px-6 sm:px-8 lg:px-12",
};

export function Container({
  children,
  className = "",
  as: Component = "div",
  size = "xl",
  padding = "md"
}: ContainerProps) {
  return (
    <Component
      className={twMerge(
        "mx-auto w-full",
        sizeStyles[size],
        paddingStyles[padding],
        className
      )}
    >
      {children}
    </Component>
  );
}
