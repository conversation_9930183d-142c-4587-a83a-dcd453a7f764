import {
  <PERSON><PERSON>,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLoaderData,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { createBrowserClient } from "@supabase/auth-helpers-remix";
import { useEffect, useState } from "react";
import { ThemeProvider } from "~/contexts/ThemeContext";

import "./tailwind.css";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,100..900;1,100..900&display=swap",
  },
  { rel: "icon", href: "/favicon.ico" },
];

export async function loader(_: LoaderFunctionArgs) {
  // Ensure we have valid values for Supabase URL and key
  const supabaseUrl = process.env.SUPABASE_URL || "";
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || "";

  return json({
    env: {
      SUPABASE_URL: supabaseUrl,
      SUPABASE_ANON_KEY: supabaseAnonKey,
    }
  });
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="h-full">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="color-scheme" content="light dark" />
        <Meta />
        <Links />
      </head>
      <body className="h-full text-render-optimized">
        <ThemeProvider>
          {children}
        </ThemeProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const { env } = useLoaderData<typeof loader>();
  
  // Use null as initial state to avoid hydration mismatch
  const [supabase, setSupabase] = useState<unknown>(null);
  
  // Initialize Supabase client on the client-side only
  useEffect(() => {
    // Only create the client if we have valid URL and key
    if (env.SUPABASE_URL && env.SUPABASE_ANON_KEY) {
      try {
        const client = createBrowserClient(
          env.SUPABASE_URL,
          env.SUPABASE_ANON_KEY
        );
        setSupabase(client);
      } catch (error) {
        console.error("Error initializing Supabase client:", error);
      }
    } else {
      console.error("Missing Supabase URL or Anon Key");
    }
  }, [env.SUPABASE_URL, env.SUPABASE_ANON_KEY]);

  return <Outlet context={{ supabase }} />;
}
