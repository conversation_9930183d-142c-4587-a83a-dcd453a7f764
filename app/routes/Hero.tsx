import { Link } from "@remix-run/react";

export default function Hero() {
  return (
    <div className="relative min-h-screen bg-surface-primary">
      {/* Hero Background */}
      <div className="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          alt="Property Care Hero"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-surface-primary/60"></div>
      </div>

      {/* Hero Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6 leading-tight">
            <span className="block">Professional</span>
            <span className="block text-accent-500">Property Care</span>
            <span className="block">Services</span>
          </h1>

          {/* Subtitle */}
          <p className="text-lg md:text-xl text-text-secondary mb-8 max-w-2xl mx-auto leading-relaxed">
            Expert property management and maintenance services for residential and commercial properties.
            Your property, our priority.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Link
              to="/book"
              className="btn-primary px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 hover:scale-105"
            >
              Book Service Now
            </Link>
            <Link
              to="/services"
              className="btn-secondary px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 hover:scale-105"
            >
              View Services
            </Link>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 max-w-3xl mx-auto">
            <div className="bg-surface-secondary/80 backdrop-blur-sm rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-accent-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-surface-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">24/7 Service</h3>
              <p className="text-text-secondary text-sm">Round-the-clock property care and emergency response</p>
            </div>

            <div className="bg-surface-secondary/80 backdrop-blur-sm rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-accent-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-surface-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Expert Team</h3>
              <p className="text-text-secondary text-sm">Certified professionals with years of experience</p>
            </div>

            <div className="bg-surface-secondary/80 backdrop-blur-sm rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-accent-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-surface-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Trusted Care</h3>
              <p className="text-text-secondary text-sm">Reliable service you can count on for your property</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
