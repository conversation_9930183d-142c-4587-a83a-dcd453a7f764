import type { MetaFunction } from "@remix-run/node";
import { <PERSON> } from "@remix-run/react";
import { Container } from "~/components/ui/Container";
import { Button, ButtonLink } from "~/components/ui/Button";
import { MobileLayout } from "~/components/layout/MobileLayout";

export const meta: MetaFunction = () => {
  return [
    { title: "Sia Moon Property Care - Reliable Property Services for Island Living" },
    { name: "description", content: "Professional property care services including cleaning, pool maintenance, repairs, and emergency check-ins for villa owners across the island." },
  ];
};

// Services data - can be moved to a database later
const services = [
  {
    id: "cleaning",
    name: "Cleaning Services",
    description: "Regular and deep cleaning services for your property, ensuring it remains pristine for you and your guests.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
      </svg>
    ),
  },
  {
    id: "pool",
    name: "Pool Maintenance",
    description: "Complete pool care including cleaning, chemical balancing, and equipment checks to keep your pool sparkling and safe.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
      </svg>
    ),
  },
  {
    id: "maintenance",
    name: "General Maintenance",
    description: "Preventative maintenance and upkeep to protect your investment and prevent small issues from becoming major problems.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
      </svg>
    ),
  },
  {
    id: "emergency",
    name: "Emergency Check-ins",
    description: "Rapid response to emergencies, weather events, or unexpected issues when you're away from your property.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
      </svg>
    ),
  },
  {
    id: "repairs",
    name: "Repairs & Renovations",
    description: "Professional repair services for everything from minor fixes to major renovations, with quality workmanship guaranteed.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21.75 6.75a4.5 4.5 0 01-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 11-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 016.336-4.486l-3.276 3.276a3.004 3.004 0 002.25 2.25l3.276-3.276c.256.565.398 1.192.398 1.852z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.867 19.125h.008v.008h-.008v-.008z" />
      </svg>
    ),
  },
];

export default function Index() {
  // Replace with your actual WhatsApp number
  const whatsappNumber = "1234567890";
  const whatsappUrl = `https://wa.me/${whatsappNumber}`;

  return (
    <MobileLayout>
      {/* Hero Section - Optimized for mobile */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-8 px-4 sm:py-12 md:py-20">
        <Container>
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-2xl sm:text-3xl md:text-5xl font-bold mb-3 sm:mb-4 leading-tight">
              Sia Moon Property Care
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl font-medium mb-3 sm:mb-4">
              Reliable Property Services for Island Living
            </p>
            <p className="text-sm sm:text-base md:text-lg opacity-90 mb-6 sm:mb-8 px-2">
              Cleaning, Pool Care, Maintenance, Emergency Check-ins & Repairs for your rental property.
            </p>
            <ButtonLink 
              to={whatsappUrl} 
              variant="secondary" 
              size="lg"
              className="font-semibold w-full sm:w-auto"
              target="_blank"
              rel="noopener noreferrer"
            >
              📱 Contact Us on WhatsApp
            </ButtonLink>
          </div>
        </Container>
      </section>

      {/* Services Section - Mobile-first grid */}
      <section className="py-8 sm:py-12 md:py-16 bg-white">
        <Container>
          <div className="text-center mb-8 sm:mb-10 px-4">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-3 sm:mb-4">Our Services</h2>
            <p className="text-gray-600 text-sm sm:text-base max-w-2xl mx-auto leading-relaxed">
              We provide comprehensive property care services to keep your island property in perfect condition, whether you're there or away.
            </p>
          </div>
          
          {/* Single column on mobile, responsive grid on larger screens */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 px-4">
            {services.map((service) => (
              <div 
                key={service.id}
                className="bg-gray-50 rounded-lg p-4 sm:p-6 shadow-sm hover:shadow-md transition-all duration-300 touch-manipulation"
              >
                <div className="flex flex-col items-center text-center">
                  <div className="mb-3 sm:mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">{service.name}</h3>
                  <p className="text-gray-600 text-sm sm:text-base leading-relaxed">{service.description}</p>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section - Mobile optimized */}
      <section className="py-8 sm:py-12 md:py-16 bg-gray-50">
        <Container>
          <div className="bg-blue-600 rounded-xl text-white p-6 sm:p-8 md:p-10 text-center mx-4">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4">Ready to simplify your property care?</h2>
            <p className="text-base sm:text-lg md:text-xl opacity-90 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed">
              Let us handle the details so you can enjoy your property without the stress of maintenance and upkeep.
            </p>
            <ButtonLink 
              to={whatsappUrl} 
              variant="secondary" 
              size="lg"
              className="font-semibold w-full sm:w-auto"
              target="_blank"
              rel="noopener noreferrer"
            >
              📱 Get Started Today
            </ButtonLink>
          </div>
        </Container>
      </section>
    </MobileLayout>
  );
}
