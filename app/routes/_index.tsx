import type { MetaFunction } from "@remix-run/node";
import { Container } from "~/components/ui/Container";
import { ButtonLink } from "~/components/ui/Button";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Card } from "~/components/ui/Card";
import { ThemeToggle } from "~/components/ui/ThemeToggle";

export const meta: MetaFunction = () => {
  return [
    { title: "Sia Moon Property Care - Reliable Property Services for Island Living" },
    { name: "description", content: "Professional property care services including cleaning, pool maintenance, repairs, and emergency check-ins for villa owners across the island." },
  ];
};

// Services data with modern AI-inspired icons
const services = [
  {
    id: "cleaning",
    name: "Cleaning Services",
    description: "Regular and deep cleaning services for your property, ensuring it remains pristine for you and your guests.",
    gradient: "from-primary-500 to-secondary-500",
    icon: (
      <div className="p-3 rounded-2xl bg-gradient-to-br from-primary-500 to-secondary-500 text-white shadow-glow">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
        </svg>
      </div>
    ),
  },
  {
    id: "pool",
    name: "Pool Maintenance",
    description: "Complete pool care including cleaning, chemical balancing, and equipment checks to keep your pool sparkling and safe.",
    gradient: "from-accent-500 to-primary-500",
    icon: (
      <div className="p-3 rounded-2xl bg-gradient-to-br from-accent-500 to-primary-500 text-white shadow-glow-teal">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
        </svg>
      </div>
    ),
  },
  {
    id: "maintenance",
    name: "General Maintenance",
    description: "Preventative maintenance and upkeep to protect your investment and prevent small issues from becoming major problems.",
    gradient: "from-secondary-500 to-accent-500",
    icon: (
      <div className="p-3 rounded-2xl bg-gradient-to-br from-secondary-500 to-accent-500 text-white shadow-glow">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
        </svg>
      </div>
    ),
  },
  {
    id: "emergency",
    name: "Emergency Check-ins",
    description: "Rapid response to emergencies, weather events, or unexpected issues when you're away from your property.",
    gradient: "from-warning-500 to-error-500",
    icon: (
      <div className="p-3 rounded-2xl bg-gradient-to-br from-warning-500 to-error-500 text-white shadow-glow">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
        </svg>
      </div>
    ),
  },
  {
    id: "repairs",
    name: "Repairs & Renovations",
    description: "Professional repair services for everything from minor fixes to major renovations, with quality workmanship guaranteed.",
    gradient: "from-neutral-600 to-neutral-800",
    icon: (
      <div className="p-3 rounded-2xl bg-gradient-to-br from-neutral-600 to-neutral-800 text-white shadow-glow">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21.75 6.75a4.5 4.5 0 01-4.884 4.484c-1.076-.091-2.264.071-2.95.904l-7.152 8.684a2.548 2.548 0 11-3.586-3.586l8.684-7.152c.833-.686.995-1.874.904-2.95a4.5 4.5 0 016.336-4.486l-3.276 3.276a3.004 3.004 0 002.25 2.25l3.276-3.276c.256.565.398 1.192.398 1.852z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.867 19.125h.008v.008h-.008v-.008z" />
        </svg>
      </div>
    ),
  },
];

export default function Index() {
  // Replace with your actual WhatsApp number
  const whatsappNumber = "1234567890";
  const whatsappUrl = `https://wa.me/${whatsappNumber}`;

  return (
    <MobileLayout>
      {/* Hero Section - Modern AI-inspired design */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 animate-gradient">
        {/* Background decorative elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-white rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-white rounded-full blur-3xl animate-pulse-soft"></div>
        </div>

        {/* Theme toggle in top right */}
        <div className="absolute top-6 right-6 z-10">
          <ThemeToggle />
        </div>

        <Container className="relative z-10">
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="animate-fade-in-up">
              <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
                <span className="block">Sia Moon</span>
                <span className="block gradient-text bg-gradient-to-r from-white to-neutral-200 bg-clip-text text-transparent">
                  Property Care
                </span>
              </h1>

              <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-medium mb-4 sm:mb-6 opacity-95">
                AI-Enhanced Property Services for Island Living
              </p>

              <p className="text-base sm:text-lg md:text-xl opacity-80 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed">
                Experience the future of property care with our intelligent cleaning, pool maintenance, repairs, and emergency services powered by modern technology.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <ButtonLink
                  to="/book"
                  variant="glass"
                  size="xl"
                  glow
                  className="font-semibold w-full sm:w-auto transform hover:scale-105 transition-all duration-300"
                >
                  🚀 Book Service Now
                </ButtonLink>

                <ButtonLink
                  to={whatsappUrl}
                  variant="outline"
                  size="xl"
                  className="font-semibold w-full sm:w-auto bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white/20"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  💬 WhatsApp Chat
                </ButtonLink>
              </div>
            </div>
          </div>
        </Container>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg className="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* Services Section - Modern card design */}
      <section className="py-16 sm:py-20 md:py-24 relative">
        <Container>
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 sm:mb-6">
              Our <span className="gradient-text">AI-Enhanced</span> Services
            </h2>
            <p className="text-neutral-600 dark:text-neutral-400 text-base sm:text-lg md:text-xl max-w-3xl mx-auto leading-relaxed">
              Experience the future of property care with our intelligent, technology-driven services designed to keep your island property in perfect condition.
            </p>
          </div>

          {/* Modern grid layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {services.map((service, index) => (
              <Card
                key={service.id}
                variant="glass"
                hover
                className="group animate-fade-in-up touch-manipulation"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="mb-6 transform group-hover:scale-110 transition-transform duration-300">
                    {service.icon}
                  </div>
                  <h3 className="text-lg sm:text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                    {service.name}
                  </h3>
                  <p className="text-neutral-600 dark:text-neutral-400 text-sm sm:text-base leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </Container>
      </section>

      {/* CTA Section - Modern gradient design */}
      <section className="py-16 sm:py-20 md:py-24 relative overflow-hidden">
        <Container>
          <Card
            variant="gradient"
            className="relative overflow-hidden bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 text-white animate-gradient"
            padding="lg"
          >
            {/* Background decorative elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 right-10 w-32 h-32 bg-white rounded-full blur-2xl animate-float"></div>
              <div className="absolute bottom-10 left-10 w-40 h-40 bg-white rounded-full blur-2xl animate-float" style={{ animationDelay: '1.5s' }}></div>
            </div>

            <div className="relative z-10 text-center max-w-4xl mx-auto">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6">
                Ready to Experience the Future of Property Care?
              </h2>
              <p className="text-base sm:text-lg md:text-xl opacity-90 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed">
                Join hundreds of satisfied property owners who trust our AI-enhanced services to keep their island properties in perfect condition, 24/7.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <ButtonLink
                  to="/book"
                  variant="glass"
                  size="xl"
                  glow
                  className="font-semibold w-full sm:w-auto transform hover:scale-105 transition-all duration-300"
                >
                  🚀 Book Your First Service
                </ButtonLink>

                <ButtonLink
                  to={whatsappUrl}
                  variant="outline"
                  size="xl"
                  className="font-semibold w-full sm:w-auto bg-white/10 backdrop-blur-sm border-white/30 text-white hover:bg-white/20"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  💬 Chat with Our Team
                </ButtonLink>
              </div>
            </div>
          </Card>
        </Container>
      </section>
    </MobileLayout>
  );
}
