import type { MetaFunction } from "@remix-run/node";
import { Container } from "~/components/ui/Container";
import { MobileLayout } from "~/components/layout/MobileLayout";

export const meta: MetaFunction = () => {
  return [
    { title: "About Us - Sia Moon Property Care" },
    { name: "description", content: "Learn about Sia Moon Property Care - your trusted partner for professional property services across the island." },
  ];
};

export default function About() {
  return (
    <MobileLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-12 px-4">
        <Container>
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">About Sia Moon Property Care</h1>
            <p className="text-lg md:text-xl opacity-90">
              Your trusted partner for professional property services across the island
            </p>
          </div>
        </Container>
      </section>

      {/* Our Story */}
      <section className="py-12 px-4">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  Sia Moon Property Care was born from a simple understanding: property owners need reliable, 
                  professional services they can trust, especially when they're not on the island.
                </p>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  As part of the established Sia Moon brand, we bring years of experience and a deep 
                  understanding of island living to every property we care for.
                </p>
                <p className="text-gray-600 leading-relaxed">
                  Whether you're a villa owner, rental property manager, or simply need reliable 
                  maintenance services, we're here to make property care effortless.
                </p>
              </div>
              <div className="bg-gray-100 rounded-lg p-8 text-center">
                <div className="text-4xl mb-4">🏝️</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Island Expertise</h3>
                <p className="text-gray-600">
                  We understand the unique challenges of island property care and maintenance.
                </p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Our Values */}
      <section className="py-12 px-4 bg-gray-50">
        <Container>
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center mb-12">Our Values</h2>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Reliability</h3>
                <p className="text-gray-600">
                  We show up when we say we will and deliver consistent, quality service every time.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Responsiveness</h3>
                <p className="text-gray-600">
                  Quick response times for emergencies and clear communication throughout every service.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Care</h3>
                <p className="text-gray-600">
                  We treat every property as if it were our own, with attention to detail and genuine care.
                </p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Our Team */}
      <section className="py-12 px-4">
        <Container>
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Our Team</h2>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Our experienced team of property care professionals is dedicated to maintaining 
              your property to the highest standards. From routine cleaning to emergency repairs, 
              we have the expertise to handle it all.
            </p>
            
            <div className="bg-blue-50 rounded-lg p-6 md:p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Why Choose Us?</h3>
              <div className="grid md:grid-cols-2 gap-4 text-left">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-600 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">Licensed and insured professionals</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-600 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">24/7 emergency response available</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-600 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">Detailed photo reports after each service</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-blue-600 mt-1 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-gray-700">Flexible scheduling to fit your needs</span>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Contact CTA */}
      <section className="py-12 px-4 bg-gray-50">
        <Container>
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">Ready to Get Started?</h2>
            <p className="text-lg text-gray-600 mb-8">
              Let us take care of your property so you can focus on what matters most.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://wa.me/1234567890"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                📱 Contact Us on WhatsApp
              </a>
              <a
                href="/book"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Book a Service
              </a>
            </div>
          </div>
        </Container>
      </section>
    </MobileLayout>
  );
}
