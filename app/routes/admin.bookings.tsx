import { json, redirect } from "@remix-run/node";
import { useL<PERSON>derD<PERSON>, Link, Form } from "@remix-run/react";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return redirect("/login");
  }
  
  // Get user profile to check role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', session.user.id)
    .single();
  
  // Get all bookings with property and user information
  const { data: bookings, error } = await supabase
    .from('bookings')
    .select(`
      *,
      properties (
        id,
        name,
        address
      ),
      user_profiles (
        id,
        name,
        email,
        phone
      )
    `)
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('Error fetching bookings:', error);
  }
  
  return json({
    user: {
      id: session.user.id,
      email: session.user.email,
      role: profile?.role || 'admin',
      name: profile?.name || 'Admin User'
    },
    bookings: bookings || []
  }, {
    headers: response.headers,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  const formData = await request.formData();
  const action = formData.get("action") as string;
  const bookingId = formData.get("bookingId") as string;
  
  if (action === "updateStatus") {
    const status = formData.get("status") as string;
    
    const { error } = await supabase
      .from('bookings')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);
    
    if (error) {
      return json({ error: error.message });
    }
    
    return json({ success: "Booking status updated" });
  }
  
  if (action === "assignStaff") {
    const staffId = formData.get("staffId") as string;
    
    const { error } = await supabase
      .from('bookings')
      .update({ 
        assigned_staff_id: staffId,
        status: 'assigned',
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId);
    
    if (error) {
      return json({ error: error.message });
    }
    
    return json({ success: "Staff assigned to booking" });
  }
  
  return json({ error: "Invalid action" });
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  confirmed: 'bg-blue-100 text-blue-800',
  assigned: 'bg-purple-100 text-purple-800',
  in_progress: 'bg-orange-100 text-orange-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800'
};

const urgencyColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  emergency: 'bg-red-100 text-red-800'
};

export default function AdminBookings() {
  const { user, bookings } = useLoaderData<typeof loader>();
  
  const getStatusBadge = (status: string) => {
    const colorClass = statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800';
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };
  
  const getUrgencyBadge = (urgency: string) => {
    const colorClass = urgencyColors[urgency as keyof typeof urgencyColors] || 'bg-gray-100 text-gray-800';
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {urgency.toUpperCase()}
      </span>
    );
  };
  
  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Bookings Management</h1>
              <p className="text-gray-600">Manage all service bookings and assignments</p>
            </div>
            <Link
              to="/admin"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              ← Back to Admin
            </Link>
          </div>
          
          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-yellow-600">
                {bookings.filter(b => b.status === 'pending').length}
              </div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-blue-600">
                {bookings.filter(b => b.status === 'confirmed').length}
              </div>
              <div className="text-sm text-gray-600">Confirmed</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-orange-600">
                {bookings.filter(b => b.status === 'in_progress').length}
              </div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="text-2xl font-bold text-green-600">
                {bookings.filter(b => b.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
          
          {/* Bookings List */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">All Bookings</h2>
            </div>
            
            {bookings.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No bookings found.
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {bookings.map((booking) => (
                  <div key={booking.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-gray-900">
                            {booking.service_type.replace('_', ' ').toUpperCase()}
                          </h3>
                          {getStatusBadge(booking.status)}
                          {getUrgencyBadge(booking.urgency)}
                        </div>
                        
                        <div className="space-y-2 text-sm text-gray-600">
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            {booking.properties?.name} - {booking.properties?.address}
                          </div>
                          
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            {booking.user_profiles?.name} - {booking.user_profiles?.email}
                          </div>
                          
                          <div className="flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {new Date(booking.preferred_date).toLocaleDateString()} at {booking.preferred_time}
                          </div>
                          
                          {booking.special_instructions && (
                            <div className="flex items-start">
                              <svg className="w-4 h-4 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              <span className="italic">{booking.special_instructions}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="ml-4 flex flex-col space-y-2">
                        <Form method="post" className="inline">
                          <input type="hidden" name="action" value="updateStatus" />
                          <input type="hidden" name="bookingId" value={booking.id} />
                          <select
                            name="status"
                            defaultValue={booking.status}
                            onChange={(e) => {
                              const form = e.target.closest('form') as HTMLFormElement;
                              form.submit();
                            }}
                            className="text-xs border border-gray-300 rounded px-2 py-1"
                          >
                            <option value="pending">Pending</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="assigned">Assigned</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                          </select>
                        </Form>
                        
                        <Link
                          to={`/admin/bookings/${booking.id}`}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>
    </MobileLayout>
  );
}
