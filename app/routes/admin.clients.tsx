import { json, redirect } from "@remix-run/node";
import { useLoaderData, Link, Form } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    return redirect("/login");
  }
  
  // Get user profile to check role
  const { data: profile } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', session.user.id)
    .single();
  
  // Get all clients with their properties and booking counts
  const { data: clients, error } = await supabase
    .from('user_profiles')
    .select(`
      *,
      properties (
        id,
        name,
        address
      ),
      bookings (
        id,
        status,
        created_at
      )
    `)
    .eq('role', 'client')
    .order('created_at', { ascending: false });
  
  if (error) {
    console.error('Error fetching clients:', error);
  }
  
  return json({
    user: {
      id: session.user.id,
      email: session.user.email,
      role: profile?.role || 'admin',
      name: profile?.name || 'Admin User'
    },
    clients: clients || []
  }, {
    headers: response.headers,
  });
}

export default function AdminClients() {
  const { user, clients } = useLoaderData<typeof loader>();
  
  const getClientStats = (client: any) => {
    const properties = client.properties?.length || 0;
    const totalBookings = client.bookings?.length || 0;
    const activeBookings = client.bookings?.filter((b: any) => 
      ['pending', 'confirmed', 'assigned', 'in_progress'].includes(b.status)
    ).length || 0;
    const completedBookings = client.bookings?.filter((b: any) => b.status === 'completed').length || 0;
    
    return { properties, totalBookings, activeBookings, completedBookings };
  };
  
  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Clients Management</h1>
              <p className="text-gray-600">Manage all client accounts and information</p>
            </div>
            <Link
              to="/admin"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              ← Back to Admin
            </Link>
          </div>
          
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-bold text-gray-900">{clients.length}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Properties</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {clients.reduce((sum, client) => sum + (client.properties?.length || 0), 0)}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {clients.reduce((sum, client) => {
                      const activeBookings = client.bookings?.filter((b: any) => 
                        ['pending', 'confirmed', 'assigned', 'in_progress'].includes(b.status)
                      ).length || 0;
                      return sum + activeBookings;
                    }, 0)}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed Services</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {clients.reduce((sum, client) => {
                      const completedBookings = client.bookings?.filter((b: any) => b.status === 'completed').length || 0;
                      return sum + completedBookings;
                    }, 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Clients List */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">All Clients</h2>
            </div>
            
            {clients.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                No clients found.
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {clients.map((client) => {
                  const stats = getClientStats(client);
                  return (
                    <div key={client.id} className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-blue-600">
                                {client.name?.charAt(0).toUpperCase() || client.email?.charAt(0).toUpperCase()}
                              </span>
                            </div>
                            <div>
                              <h3 className="text-lg font-medium text-gray-900">
                                {client.name || 'Unnamed Client'}
                              </h3>
                              <p className="text-sm text-gray-600">{client.email}</p>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <div className="text-lg font-semibold text-gray-900">{stats.properties}</div>
                              <div className="text-xs text-gray-600">Properties</div>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <div className="text-lg font-semibold text-gray-900">{stats.totalBookings}</div>
                              <div className="text-xs text-gray-600">Total Bookings</div>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <div className="text-lg font-semibold text-orange-600">{stats.activeBookings}</div>
                              <div className="text-xs text-gray-600">Active</div>
                            </div>
                            <div className="text-center p-3 bg-gray-50 rounded-lg">
                              <div className="text-lg font-semibold text-green-600">{stats.completedBookings}</div>
                              <div className="text-xs text-gray-600">Completed</div>
                            </div>
                          </div>
                          
                          <div className="space-y-2 text-sm text-gray-600">
                            {client.phone && (
                              <div className="flex items-center">
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                {client.phone}
                              </div>
                            )}
                            
                            <div className="flex items-center">
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              Joined {new Date(client.created_at).toLocaleDateString()}
                            </div>
                            
                            {client.properties && client.properties.length > 0 && (
                              <div className="mt-3">
                                <h4 className="font-medium text-gray-900 mb-2">Properties:</h4>
                                <div className="space-y-1">
                                  {client.properties.slice(0, 3).map((property: any) => (
                                    <div key={property.id} className="flex items-center text-xs">
                                      <svg className="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                      </svg>
                                      {property.name} - {property.address}
                                    </div>
                                  ))}
                                  {client.properties.length > 3 && (
                                    <div className="text-xs text-gray-500">
                                      +{client.properties.length - 3} more properties
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="ml-4 flex flex-col space-y-2">
                          <Link
                            to={`/admin/clients/${client.id}`}
                            className="text-sm text-blue-600 hover:text-blue-800"
                          >
                            View Details
                          </Link>
                          
                          <a
                            href={`mailto:${client.email}`}
                            className="text-sm text-gray-600 hover:text-gray-800"
                          >
                            Send Email
                          </a>
                          
                          {client.phone && (
                            <a
                              href={`https://wa.me/${client.phone.replace(/\D/g, '')}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-sm text-green-600 hover:text-green-800"
                            >
                              WhatsApp
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </Container>
    </MobileLayout>
  );
}
