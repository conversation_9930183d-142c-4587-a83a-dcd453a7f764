import { useState } from "react";
import { Form } from "@remix-run/react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { 
  FormField, 
  Input, 
  Select, 
  TextArea, 
  PhoneInput, 
  EmailInput, 
  DateInput, 
  TimeInput,
  FileInput,
  Checkbox,
  RadioGroup 
} from "~/components/forms/MobileForm";
import { BottomSheet, ActionSheet } from "~/components/ui/BottomSheet";

/*
 * MOBILE-FIRST BOOKING FORM
 * 
 * This is an example of how to build mobile-optimized forms
 * Key mobile UX patterns demonstrated:
 * - Step-by-step form flow
 * - Large touch targets
 * - Mobile-friendly input types
 * - Photo upload with camera integration
 * - Bottom sheet for service selection
 * - Progressive form validation
 */

const services = [
  { id: "cleaning", name: "Cleaning Services", price: "$150" },
  { id: "pool", name: "Pool Maintenance", price: "$100" },
  { id: "maintenance", name: "General Maintenance", price: "$200" },
  { id: "emergency", name: "Emergency Check-in", price: "$75" },
  { id: "repairs", name: "Repairs & Renovations", price: "Quote needed" },
];

const urgencyOptions = [
  { value: "low", label: "Low Priority", description: "Within 1-2 weeks" },
  { value: "medium", label: "Medium Priority", description: "Within a few days" },
  { value: "high", label: "High Priority", description: "Within 24-48 hours" },
  { value: "emergency", label: "Emergency", description: "Immediate attention needed" },
];

export default function BookingForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [showServiceSheet, setShowServiceSheet] = useState(false);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    propertyAddress: "",
    services: [] as string[],
    preferredDate: "",
    preferredTime: "",
    urgency: "",
    specialInstructions: "",
    photos: [] as File[],
    agreedToTerms: false,
    allowWhatsApp: false,
  });

  const totalSteps = 4;

  const handleServiceToggle = (serviceId: string) => {
    const updated = selectedServices.includes(serviceId)
      ? selectedServices.filter(id => id !== serviceId)
      : [...selectedServices, serviceId];
    
    setSelectedServices(updated);
    setFormData(prev => ({ ...prev, services: updated }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFormData(prev => ({
        ...prev,
        photos: [...prev.photos, ...newFiles]
      }));
    }
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const canProceedFromStep = (step: number) => {
    switch (step) {
      case 1:
        return formData.name && formData.email && formData.phone && formData.propertyAddress;
      case 2:
        return formData.services.length > 0;
      case 3:
        return formData.preferredDate && formData.urgency;
      default:
        return true;
    }
  };

  return (
    <MobileLayout hideNavBar>
      <div className="bg-gray-50 min-h-screen">
        {/* Progress Header */}
        <div className="bg-white border-b border-gray-200 sticky top-16 z-20">
          <Container>
            <div className="px-4 py-4">
              <div className="flex items-center justify-between mb-3">
                <h1 className="text-xl font-bold text-gray-900">Book Service</h1>
                <span className="text-sm text-gray-500">
                  Step {currentStep} of {totalSteps}
                </span>
              </div>
              
              {/* Progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentStep / totalSteps) * 100}%` }}
                />
              </div>
            </div>
          </Container>
        </div>

        <Container>
          <Form method="post" className="px-4 py-6">
            
            {/* Step 1: Contact Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-2">Contact Information</h2>
                  <p className="text-sm text-gray-600">We'll use this to reach you about your booking</p>
                </div>

                <FormField label="Full Name" required>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter your full name"
                    autoComplete="name"
                  />
                </FormField>

                <FormField label="Email Address" required>
                  <EmailInput
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </FormField>

                <FormField label="Phone Number" required>
                  <PhoneInput
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="(*************"
                  />
                </FormField>

                <FormField 
                  label="Property Address" 
                  required
                  helpText="Where should we provide the service?"
                >
                  <TextArea
                    value={formData.propertyAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, propertyAddress: e.target.value }))}
                    placeholder="Enter the full property address..."
                    rows={3}
                  />
                </FormField>

                <Checkbox
                  label="Allow WhatsApp communications"
                  description="Get updates and photos via WhatsApp"
                  checked={formData.allowWhatsApp}
                  onChange={(e) => setFormData(prev => ({ ...prev, allowWhatsApp: e.target.checked }))}
                />
              </div>
            )}

            {/* Step 2: Service Selection */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-2">Select Services</h2>
                  <p className="text-sm text-gray-600">Choose the services you need</p>
                </div>

                <div className="space-y-3">
                  {services.map((service) => (
                    <label 
                      key={service.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg cursor-pointer touch-manipulation hover:bg-gray-50"
                    >
                      <div className="flex items-start space-x-3">
                        <input
                          type="checkbox"
                          checked={selectedServices.includes(service.id)}
                          onChange={() => handleServiceToggle(service.id)}
                          className="mt-1 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <div>
                          <h3 className="font-medium text-gray-900">{service.name}</h3>
                          <p className="text-sm text-gray-600">Starting from {service.price}</p>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>

                {selectedServices.length > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">Selected Services:</h3>
                    <ul className="text-sm text-blue-800 space-y-1">
                      {selectedServices.map(serviceId => {
                        const service = services.find(s => s.id === serviceId);
                        return service ? <li key={serviceId}>• {service.name}</li> : null;
                      })}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Scheduling */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-2">Schedule Service</h2>
                  <p className="text-sm text-gray-600">When would you like us to come?</p>
                </div>

                <FormField label="Preferred Date" required>
                  <DateInput
                    value={formData.preferredDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, preferredDate: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </FormField>

                <FormField label="Preferred Time">
                  <TimeInput
                    value={formData.preferredTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, preferredTime: e.target.value }))}
                  />
                </FormField>

                <FormField label="Priority Level" required>
                  <RadioGroup
                    name="urgency"
                    value={formData.urgency}
                    onChange={(value) => setFormData(prev => ({ ...prev, urgency: value }))}
                    options={urgencyOptions}
                  />
                </FormField>
              </div>
            )}

            {/* Step 4: Additional Details */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-2">Additional Details</h2>
                  <p className="text-sm text-gray-600">Help us serve you better</p>
                </div>

                <FormField 
                  label="Special Instructions" 
                  helpText="Any specific requirements or access instructions"
                >
                  <TextArea
                    value={formData.specialInstructions}
                    onChange={(e) => setFormData(prev => ({ ...prev, specialInstructions: e.target.value }))}
                    placeholder="e.g., Key location, special access requirements, areas to focus on..."
                    rows={4}
                  />
                </FormField>

                <FormField 
                  label="Photos (Optional)" 
                  helpText="Share photos of areas that need attention"
                >
                  <FileInput
                    onChange={handleFileChange}
                    multiple
                    accept="image/*"
                    capture // Enable camera on mobile
                  />
                </FormField>

                {/* Photo previews */}
                {formData.photos.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="text-sm font-medium text-gray-700">Uploaded Photos:</h3>
                    <div className="grid grid-cols-3 gap-2">
                      {formData.photos.map((photo, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(photo)}
                            alt={`Upload ${index + 1}`}
                            className="w-full h-20 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removePhoto(index)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Checkbox
                  label="I agree to the Terms and Conditions"
                  description="Required to proceed with booking"
                  checked={formData.agreedToTerms}
                  onChange={(e) => setFormData(prev => ({ ...prev, agreedToTerms: e.target.checked }))}
                />
              </div>
            )}

            {/* Navigation buttons */}
            <div className="flex space-x-3 pt-8 pb-4">
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="lg"
                  onClick={handlePrev}
                  className="flex-1"
                >
                  Previous
                </Button>
              )}
              
              {currentStep < totalSteps ? (
                <Button
                  type="button"
                  variant="primary"
                  size="lg"
                  onClick={handleNext}
                  disabled={!canProceedFromStep(currentStep)}
                  className="flex-1"
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  disabled={!formData.agreedToTerms}
                  className="flex-1"
                >
                  Submit Booking
                </Button>
              )}
            </div>
          </Form>
        </Container>
      </div>
    </MobileLayout>
  );
}
