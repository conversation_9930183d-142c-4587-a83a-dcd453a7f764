import { useState, useEffect, ChangeEvent } from "react";
import { Form, useActionData, useNavigation, useLoaderData } from "@remix-run/react";
import { json, redirect } from "@remix-run/node";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { 
  FormField, 
  Input, 
  Select, 
  TextArea, 
  DateInput
} from "~/components/forms/MobileForm";
import { requireAuth, getUserRole } from "~/utils/supabase.server";
import type { Database } from "~/types/database";

/**
 * Book a Service Page for Sia Moon Property Care
 * 
 * This page allows authenticated clients to book property care services.
 * Features:
 * - Service type selection
 * - Property selection from user's properties
 * - Date and time scheduling
 * - Urgency level selection
 * - Additional notes
 * - Form validation and submission to Supabase
 */

// Service types available for booking
const SERVICE_TYPES = [
  { value: "cleaning", label: "Cleaning" },
  { value: "pool_cleaning", label: "Pool Cleaning" },
  { value: "maintenance", label: "Maintenance" },
  { value: "repairs", label: "Repairs" },
  { value: "emergency_check", label: "Emergency Check-in" }
];

// Time slots available for booking
const TIME_SLOTS = [
  { value: "morning", label: "Morning (8am - 12pm)" },
  { value: "afternoon", label: "Afternoon (12pm - 4pm)" },
  { value: "evening", label: "Evening (4pm - 8pm)" }
];

// Urgency levels
const URGENCY_LEVELS = [
  { value: "normal", label: "Normal", description: "Standard scheduling" },
  { value: "emergency", label: "Emergency", description: "Urgent attention required" }
];

// Type for the form data
type BookingFormData = {
  serviceType: string;
  propertyId: string;
  preferredDate: string;
  timeSlot: string;
  urgency: string;
  notes: string;
};

// Type for properties from Supabase
type Property = Database["public"]["Tables"]["properties"]["Row"];

// Loader function to check authentication and fetch user properties
export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  
  try {
    // Check if user is authenticated and has client role
    const role = await getUserRole(request, response);
    
    if (role !== "client") {
      throw new Error("Only clients can book services");
    }
    
    // Get user session and fetch their properties
    const { supabase, session } = await requireAuth(request, response);
    
    const { data: properties, error } = await supabase
      .from("properties")
      .select("*")
      .eq("user_id", session.user.id);
      
    if (error) {
      throw new Error(error.message);
    }
    
    return json({ 
      properties: properties || [],
      userId: session.user.id
    }, {
      headers: response.headers
    });
  } catch (error) {
    if (error instanceof Error && error.message === "Only clients can book services") {
      // Redirect to unauthorized page for non-client users
      throw redirect("/unauthorized", {
        headers: response.headers
      });
    }
    
    // Redirect to login for unauthenticated users
    throw redirect("/login", {
      headers: response.headers
    });
  }
}

// Action function to handle form submission
export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  
  try {
    // Check authentication and get session
    const { supabase, session } = await requireAuth(request, response);
    
    // Parse form data
    const formData = await request.formData();
    const serviceType = formData.get("serviceType") as string;
    const propertyId = formData.get("propertyId") as string;
    const preferredDate = formData.get("preferredDate") as string;
    const timeSlot = formData.get("timeSlot") as string;
    const urgency = formData.get("urgency") as string;
    const notes = formData.get("notes") as string;
    
    // Validate required fields
    if (!serviceType || !propertyId || !preferredDate || !timeSlot || !urgency) {
      return json({ 
        success: false, 
        error: "All fields except notes are required" 
      }, {
        headers: response.headers
      });
    }
    
    // Validate date is not in the past
    // Ensure date is parsed in local time zone for comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(preferredDate + "T00:00:00");
    selectedDate.setHours(0, 0, 0, 0);
    if (selectedDate < today) {
      return json({ 
        success: false, 
        error: "Selected date cannot be in the past" 
      }, {
        headers: response.headers
      });
    }
    
    // Insert booking into Supabase
    const { data, error } = await supabase
      .from("bookings")
      .insert({
        user_id: session.user.id,
        property_id: propertyId,
        service_type: serviceType,
        preferred_date: preferredDate,
        time_slot: timeSlot,
        urgency: urgency,
        notes: notes || null,
        status: "pending"
      })
      .select();
      
    if (error) {
      return json({ 
        success: false, 
        error: error.message 
      }, {
        headers: response.headers
      });
    }
    
    // Return success response
    return json({ 
      success: true, 
      booking: data[0] 
    }, {
      headers: response.headers
    });
    
  } catch (error) {
    // Handle authentication errors
    return redirect("/login", {
      headers: response.headers
    });
  }
}

export default function BookingForm() {
  // Get loader data (properties and userId)
  const loaderData = useLoaderData<typeof loader>();
  // Ensure loaderData is always defined and has properties, userId
  const properties: Property[] = loaderData?.properties ?? [];
  const userId: string = loaderData?.userId ?? "";
  // Get action data (form submission results)
  const actionData = useActionData<typeof action>();
  // Get navigation state for loading indicators
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  // Form state
  const [formData, setFormData] = useState<BookingFormData>({
    serviceType: "",
    propertyId: "",
    preferredDate: "",
    timeSlot: "",
    urgency: "normal",
    notes: ""
  });
  // Success state for showing confirmation
  const [showSuccess, setShowSuccess] = useState(false);
  // Update success state when action data changes
  useEffect(() => {
    if (actionData && typeof actionData === "object" && "success" in actionData && actionData.success) {
      setShowSuccess(true);
      // Reset form after successful submission
      setFormData({
        serviceType: "",
        propertyId: "",
        preferredDate: "",
        timeSlot: "",
        urgency: "normal",
        notes: ""
      });
      // Hide success message after 5 seconds
      const timer = setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [actionData]);
  // Handle form input changes
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  // Check if form is valid for submission
  const isFormValid = () => {
    return (
      formData.serviceType !== "" &&
      formData.propertyId !== "" &&
      formData.preferredDate !== "" &&
      formData.timeSlot !== "" &&
      formData.urgency !== "" &&
      !isDateInPast(formData.preferredDate)
    );
  };
  // Check if selected date is in the past
  const isDateInPast = (dateString: string) => {
    if (!dateString) return false;
    // Parse as local date, not UTC
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const selectedDate = new Date(dateString + "T00:00:00");
    selectedDate.setHours(0, 0, 0, 0);
    return selectedDate < today;
  };
  // Get today's date in YYYY-MM-DD format for min date attribute, using local time zone
  const getTodayString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };
  return (
    <MobileLayout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 sticky top-0 z-20">
          <Container>
            <div className="px-4 py-4">
              <h1 className="text-xl font-bold text-gray-900">Book a Service</h1>
              <p className="text-sm text-gray-500 mt-1">
                Schedule property care services for your properties
              </p>
            </div>
          </Container>
        </div>

        <Container>
          {/* Success message */}
          {showSuccess && (
            <div className="mx-4 my-4 bg-green-50 border border-green-200 rounded-lg p-4 flex items-start" role="status" aria-live="polite">
              <div className="flex-shrink-0 mr-3">
                <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-green-800">Booking Successful</h3>
                <p className="text-sm text-green-700 mt-1">
                  Your service has been booked successfully. We'll contact you to confirm the details.
                </p>
              </div>
            </div>
          )}
          {/* Error message */}
          {actionData && typeof actionData === "object" && "error" in actionData && actionData.error && !showSuccess && (
            <div className="mx-4 my-4 bg-red-50 border border-red-200 rounded-lg p-4 flex items-start" role="alert" aria-live="assertive">
              <div className="flex-shrink-0 mr-3">
                <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-red-800">Booking Failed</h3>
                <p className="text-sm text-red-700 mt-1">{actionData.error}</p>
              </div>
            </div>
          )}
          {/* No properties warning */}
          {properties.length === 0 && (
            <div className="mx-4 my-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4" role="alert" aria-live="polite">
              <h3 className="text-sm font-medium text-yellow-800">No Properties Found</h3>
              <p className="text-sm text-yellow-700 mt-1">
                You need to add at least one property before booking a service.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                aria-label="Add Property"
                onClick={() => {/* TODO: Add link to property management page */}}
              >
                Add Property
              </Button>
            </div>
          )}
          <Form method="post" className="px-4 py-6 space-y-6" aria-label="Book a Service Form">
            {/* Service Type */}
            <FormField label="Service Type" required>
              <Select
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                placeholder="Select a service"
                aria-label="Service Type"
                required
              >
                <option value="" disabled>
                  Select a service
                </option>
                {SERVICE_TYPES.map(service => (
                  <option key={service.value} value={service.value}>
                    {service.label}
                  </option>
                ))}
              </Select>
            </FormField>
            {/* Property Selection */}
            <FormField
              label="Select Property"
              required
              helpText={properties.length === 0 ? "No properties available. Please add a property first." : undefined}
            >
              <Select
                name="propertyId"
                value={formData.propertyId}
                onChange={handleInputChange}
                placeholder="Select a property"
                aria-label="Select Property"
                disabled={properties.length === 0}
                required
              >
                <option value="" disabled>
                  Select a property
                </option>
                {properties.map((property: Property) => (
                  <option key={property.id} value={property.id}>
                    {property.name} - {property.address}
                  </option>
                ))}
              </Select>
            </FormField>
            {/* Preferred Date */}
            <FormField
              label="Preferred Date"
              required
              error={isDateInPast(formData.preferredDate) ? "Date cannot be in the past" : undefined}
            >
              <DateInput
                name="preferredDate"
                value={formData.preferredDate}
                onChange={handleInputChange}
                min={getTodayString()}
                error={isDateInPast(formData.preferredDate)}
                aria-label="Preferred Date"
                required
              />
            </FormField>
            {/* Time Slot */}
            <FormField label="Preferred Time" required>
              <Select
                name="timeSlot"
                value={formData.timeSlot}
                onChange={handleInputChange}
                placeholder="Select a time slot"
                aria-label="Preferred Time"
                required
              >
                <option value="" disabled>
                  Select a time slot
                </option>
                {TIME_SLOTS.map(slot => (
                  <option key={slot.value} value={slot.value}>
                    {slot.label}
                  </option>
                ))}
              </Select>
            </FormField>
            {/* Urgency */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700" id="urgency-label">
                Urgency Level <span className="text-red-500">*</span>
              </label>
              <div className="space-y-2" role="radiogroup" aria-labelledby="urgency-label">
                {URGENCY_LEVELS.map(level => (
                  <label
                    key={level.value}
                    className="flex items-start space-x-3 cursor-pointer touch-manipulation"
                  >
                    <input
                      type="radio"
                      name="urgency"
                      value={level.value}
                      checked={formData.urgency === level.value}
                      onChange={handleInputChange}
                      className="mt-1 w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      aria-checked={formData.urgency === level.value}
                      aria-label={level.label}
                      required
                    />
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-700">{level.label}</span>
                      <p className="text-xs text-gray-500 mt-0.5">{level.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>
            {/* Additional Notes */}
            <FormField
              label="Additional Notes"
              helpText="Any specific requirements or access instructions"
            >
              <TextArea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="e.g., Gate code, pet information, specific areas to focus on..."
                rows={4}
                aria-label="Additional Notes"
              />
            </FormField>
            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              size="lg"
              isLoading={isSubmitting}
              disabled={isSubmitting || !isFormValid() || properties.length === 0}
              className="w-full mt-8"
              aria-label="Book Service"
            >
              {isSubmitting ? "Submitting..." : "Book Service"}
            </Button>
            {/* Booking policy note */}
            <p className="text-xs text-gray-500 text-center mt-4">
              By booking a service, you agree to our service terms and cancellation policy.
              {/* TODO: Add links to terms and cancellation policy */}
            </p>
          </Form>
        </Container>
      </div>
    </MobileLayout>
  );
}
