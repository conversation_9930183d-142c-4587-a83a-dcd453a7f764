import { json, redirect } from "@remix-run/node";
import { useLoaderData, useOutletContext, useNavigate } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/utils/supabase.server";
import { useEffect, useState } from "react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import type { Database } from "~/types/database";

/**
 * Client Dashboard for Sia Moon Property Care
 * 
 * Features:
 * - Personalized greeting and booking summary
 * - Active bookings list with actions (view details, cancel)
 * - Past bookings in collapsible section with re-book option
 * - Quick actions for common tasks
 */

// Type definitions for our data
type Booking = Database["public"]["Tables"]["bookings"]["Row"] & {
  property_name?: string;
  property_address?: string;
};

type LoaderData = {
  user: {
    id: string;
    email: string | null;
  };
  profile: {
    id: string;
    name: string | null;
    role: string;
    email: string;
  } | null;
  activeBookings: Booking[];
  pastBookings: Booking[];
};

// Status badge colors
const STATUS_COLORS = {
  pending: "bg-yellow-100 text-yellow-800",
  confirmed: "bg-blue-100 text-blue-800",
  completed: "bg-green-100 text-green-800",
  cancelled: "bg-gray-100 text-gray-800",
  in_progress: "bg-purple-100 text-purple-800"
};

// Format date for display
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

// Service type display names
const SERVICE_TYPES = {
  cleaning: "Cleaning",
  pool_cleaning: "Pool Cleaning",
  maintenance: "Maintenance",
  repairs: "Repairs",
  emergency_check: "Emergency Check-in"
};

// Time slot display names
const TIME_SLOTS = {
  morning: "Morning (8am - 12pm)",
  afternoon: "Afternoon (12pm - 4pm)",
  evening: "Evening (4pm - 8pm)"
};

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  
  try {
    const { supabase, session } = await requireAuth(request, response);
    
    // Get user profile
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", session.user.id)
      .single();
    
    // Check if user has client role
    if (profile?.role !== "client" && profile?.role !== "admin") {
      throw redirect("/unauthorized", { headers: response.headers });
    }
    
    // Get user's properties for lookup
    const { data: properties } = await supabase
      .from("properties")
      .select("id, name, address")
      .eq("user_id", session.user.id);
    
    // Create a map of property IDs to property details for quick lookup
    const propertyMap = (properties || []).reduce((map, property) => {
      map[property.id] = { name: property.name, address: property.address };
      return map;
    }, {} as Record<string, { name: string, address: string }>);
    
    // Get active bookings (pending, confirmed, in_progress)
    const { data: activeBookings } = await supabase
      .from("bookings")
      .select("*")
      .eq("user_id", session.user.id)
      .in("status", ["pending", "confirmed", "in_progress"])
      .order("preferred_date", { ascending: true });
    
    // Get past bookings (completed, cancelled)
    const { data: pastBookings } = await supabase
      .from("bookings")
      .select("*")
      .eq("user_id", session.user.id)
      .in("status", ["completed", "cancelled"])
      .order("preferred_date", { ascending: false })
      .limit(10); // Limit to recent 10 past bookings
    
    // Add property details to each booking
    const enhancedActiveBookings = (activeBookings || []).map(booking => ({
      ...booking,
      property_name: propertyMap[booking.property_id]?.name || "Unknown Property",
      property_address: propertyMap[booking.property_id]?.address || ""
    }));
    
    const enhancedPastBookings = (pastBookings || []).map(booking => ({
      ...booking,
      property_name: propertyMap[booking.property_id]?.name || "Unknown Property",
      property_address: propertyMap[booking.property_id]?.address || ""
    }));
    
    return json(
      { 
        user: session.user, 
        profile,
        activeBookings: enhancedActiveBookings,
        pastBookings: enhancedPastBookings
      },
      { headers: response.headers }
    );
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    return redirect("/login", { headers: response.headers });
  }
}

export default function Dashboard() {
  const { supabase } = useOutletContext<{ supabase: any }>();
  const navigate = useNavigate();
  const loaderData = useLoaderData<typeof loader>();
  const [user, setUser] = useState(loaderData?.user);
  const [profile, setProfile] = useState(loaderData?.profile);
  const [activeBookings, setActiveBookings] = useState<Booking[]>(loaderData?.activeBookings || []);
  const [pastBookings, setpastBookings] = useState<Booking[]>(loaderData?.pastBookings || []);
  const [showPastBookings, setShowPastBookings] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cancellingId, setCancellingId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Get user's first name for greeting
  const firstName = profile?.name?.split(' ')[0] || user?.email?.split('@')[0] || 'Client';
  
  // Check authentication status on client-side
  useEffect(() => {
    if (!supabase) return;
    
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data.session) {
        window.location.href = "/login";
      }
    };
    
    checkSession();
  }, [supabase]);
  
  // Function to cancel a booking
  const cancelBooking = async (bookingId: string) => {
    if (!supabase) return;
    
    try {
      setCancellingId(bookingId);
      setErrorMessage(null);
      
      const { error } = await supabase
        .from("bookings")
        .update({ status: "cancelled" })
        .eq("id", bookingId)
        .eq("user_id", user?.id); // Security: ensure user owns this booking
      
      if (error) throw new Error(error.message);
      
      // Update local state to reflect the change
      setActiveBookings(prev => 
        prev.filter(booking => booking.id !== bookingId)
      );
      
      // Add to past bookings
      const cancelledBooking = activeBookings.find(b => b.id === bookingId);
      if (cancelledBooking) {
        setpastBookings(prev => [{...cancelledBooking, status: "cancelled"}, ...prev]);
      }
      
      setSuccessMessage("Booking cancelled successfully");
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
      
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : "Failed to cancel booking");
    } finally {
      setCancellingId(null);
    }
  };
  
  // Function to handle re-booking
  const handleRebook = (booking: Booking) => {
    // Navigate to booking page with prefilled data
    navigate(`/book?rebooking=${booking.id}&service=${booking.service_type}&property=${booking.property_id}`);
  };
  
  // Show loading state if supabase is not initialized yet
  if (!supabase || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse text-center p-4">
          <div className="w-8 h-8 bg-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-gray-700">Loading Dashboard...</h2>
        </div>
      </div>
    );
  }
  
  return (
    <MobileLayout user={user ? { id: user.id, email: user.email || '', role: profile?.role } : null}>
      {/* Mobile-optimized header */}
      <header className="bg-white shadow-sm sticky top-16 z-30">
        <Container>
          <div className="py-4 px-4 flex justify-between items-center">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-600 mt-1">Welcome back, {firstName}</p>
            </div>
            <Button
              onClick={async () => {
                await supabase.auth.signOut();
                window.location.href = "/";
              }}
              variant="outline"
              size="sm"
              className="text-xs px-3 py-1.5"
            >
              Sign Out
            </Button>
          </div>
        </Container>
      </header>

      <main className="bg-gray-50 min-h-[calc(100vh-8rem)]">
        <Container>
          <div className="py-4 px-4">
            
            {/* Success/Error Messages */}
            {successMessage && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md text-green-800 text-sm flex items-center">
                <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                {successMessage}
              </div>
            )}
            
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-800 text-sm flex items-center">
                <svg className="w-5 h-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {errorMessage}
              </div>
            )}
            
            {/* Quick Actions - Mobile First */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-3">
                <Button 
                  variant="primary" 
                  className="h-20 flex-col text-sm"
                  onClick={() => navigate("/book")}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Book Service
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col text-sm"
                  onClick={() => navigate("/properties")}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Manage Properties
                </Button>
              </div>
            </div>

            {/* Active Bookings Section */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Active Bookings</h2>
                <span className="text-sm bg-blue-100 text-blue-800 py-1 px-2 rounded-full">
                  {activeBookings.length} {activeBookings.length === 1 ? 'booking' : 'bookings'}
                </span>
              </div>
              
              {activeBookings.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <h3 className="text-gray-500 text-lg font-medium mb-1">No Active Bookings</h3>
                  <p className="text-gray-400 text-sm mb-4">You don't have any active bookings at the moment.</p>
                  <Button 
                    variant="primary" 
                    size="md"
                    onClick={() => navigate("/book")}
                  >
                    Book a Service
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeBookings.map((booking) => (
                    <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      {/* Booking header with status */}
                      <div className="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900">
                            {SERVICE_TYPES[booking.service_type as keyof typeof SERVICE_TYPES] || booking.service_type}
                          </span>
                          <span className="mx-2 text-gray-300">•</span>
                          <span className="text-xs text-gray-500">
                            {formatDate(booking.preferred_date)}
                          </span>
                        </div>
                        <span className={`text-xs px-2 py-1 rounded-full ${STATUS_COLORS[booking.status as keyof typeof STATUS_COLORS]}`}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </span>
                      </div>
                      
                      {/* Booking details */}
                      <div className="px-4 py-3">
                        <div className="mb-3">
                          <h3 className="text-sm font-medium text-gray-900">{booking.property_name}</h3>
                          <p className="text-xs text-gray-500 mt-0.5">{booking.property_address}</p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3 text-xs text-gray-500 mb-3">
                          <div>
                            <span className="block text-gray-400">Date</span>
                            <span className="font-medium text-gray-900">{formatDate(booking.preferred_date)}</span>
                          </div>
                          <div>
                            <span className="block text-gray-400">Time</span>
                            <span className="font-medium text-gray-900">
                              {TIME_SLOTS[booking.time_slot as keyof typeof TIME_SLOTS] || booking.time_slot}
                            </span>
                          </div>
                          <div>
                            <span className="block text-gray-400">Urgency</span>
                            <span className="font-medium text-gray-900 capitalize">{booking.urgency}</span>
                          </div>
                          <div>
                            <span className="block text-gray-400">Booking ID</span>
                            <span className="font-medium text-gray-900">{booking.id.slice(0, 8)}</span>
                          </div>
                        </div>
                        
                        {/* Action buttons */}
                        <div className="flex space-x-2 mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => navigate(`/bookings/${booking.id}`)}
                          >
                            View Details
                          </Button>
                          
                          {booking.status === "pending" && (
                            <Button
                              variant="danger"
                              size="sm"
                              className="flex-1"
                              isLoading={cancellingId === booking.id}
                              onClick={() => cancelBooking(booking.id)}
                            >
                              Cancel
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Past Bookings Section - Collapsible */}
            <div className="mb-6">
              <button
                className="w-full flex justify-between items-center mb-3"
                onClick={() => setShowPastBookings(!showPastBookings)}
              >
                <h2 className="text-lg font-semibold text-gray-900">Past Bookings</h2>
                <div className="flex items-center">
                  <span className="text-sm bg-gray-100 text-gray-800 py-1 px-2 rounded-full mr-2">
                    {pastBookings.length}
                  </span>
                  <svg 
                    className={`w-5 h-5 text-gray-500 transform transition-transform ${showPastBookings ? 'rotate-180' : ''}`} 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              
              {showPastBookings && (
                <div className="space-y-3 mt-2">
                  {pastBookings.length === 0 ? (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                      <p className="text-gray-500 text-sm">No past bookings found</p>
                    </div>
                  ) : (
                    pastBookings.map((booking) => (
                      <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="px-4 py-3 flex justify-between items-center">
                          <div>
                            <div className="flex items-center">
                              <span className="text-sm font-medium text-gray-900">
                                {SERVICE_TYPES[booking.service_type as keyof typeof SERVICE_TYPES] || booking.service_type}
                              </span>
                              <span className="mx-2 text-gray-300">•</span>
                              <span className="text-xs text-gray-500">
                                {formatDate(booking.preferred_date)}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{booking.property_name}</p>
                          </div>
                          <div className="flex items-center">
                            <span className={`text-xs px-2 py-1 rounded-full ${STATUS_COLORS[booking.status as keyof typeof STATUS_COLORS]}`}>
                              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                            </span>
                          </div>
                        </div>
                        
                        {/* Re-book button for completed or cancelled bookings */}
                        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full text-xs"
                            onClick={() => handleRebook(booking)}
                          >
                            Re-book This Service
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Account Information Card - Mobile Optimized */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-4 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
                <p className="mt-1 text-sm text-gray-500">Your account details and preferences</p>
              </div>
              
              <div className="divide-y divide-gray-200">
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-all">{user.email}</dd>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Account Type</dt>
                    <dd className="mt-1 text-sm text-gray-900 capitalize">
                      {profile?.role || "client"}
                    </dd>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    profile?.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                    profile?.role === 'staff' ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {profile?.role || "client"}
                  </span>
                </div>
                
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Login</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(user.last_sign_in_at || Date.now()).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </dd>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </main>
    </MobileLayout>
  );
}
