import { json, redirect } from "@remix-run/node";
import { useLoaderData, useOutletContext } from "@remix-run/react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { requireAuth } from "~/utils/supabase.server";
import { useEffect } from "react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { supabase, session } = await requireAuth(request);
    
    // Get user profile
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", session.user.id)
      .single();
    
    return json({ user: session.user, profile });
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    return redirect("/login");
  }
}

export default function Dashboard() {
  const { supabase } = useOutletContext<{ supabase: any }>();
  const loaderData = useLoaderData<typeof loader>();
  const user = loaderData?.user;
  const profile = loaderData?.profile;
  
  // Check authentication status on client-side
  useEffect(() => {
    if (!supabase) return;
    
    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data.session) {
        window.location.href = "/login";
      }
    };
    
    checkSession();
  }, [supabase]);
  
  // Show loading state if supabase is not initialized yet
  if (!supabase || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse text-center p-4">
          <div className="w-8 h-8 bg-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-gray-700">Loading Dashboard...</h2>
        </div>
      </div>
    );
  }
  
  return (
    <MobileLayout user={user ? { id: user.id, email: user.email || '', role: profile?.role } : null}>
      {/* Mobile-optimized header */}
      <header className="bg-white shadow-sm sticky top-16 z-30">
        <Container>
          <div className="py-4 px-4 flex justify-between items-center">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-600 mt-1">Welcome back, {user.email?.split('@')[0]}</p>
            </div>
            <Button
              onClick={async () => {
                await supabase.auth.signOut();
                window.location.href = "/";
              }}
              variant="outline"
              size="sm"
              className="text-xs px-3 py-1.5"
            >
              Sign Out
            </Button>
          </div>
        </Container>
      </header>

      <main className="bg-gray-50 min-h-[calc(100vh-8rem)]">
        <Container>
          <div className="py-4 px-4">
            
            {/* Quick Actions - Mobile First */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-3">
                <Button 
                  variant="primary" 
                  className="h-20 flex-col text-sm"
                  onClick={() => window.location.href = "/book"}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Book Service
                </Button>
                <Button 
                  variant="outline" 
                  className="h-20 flex-col text-sm"
                  onClick={() => window.location.href = "/contact"}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  Contact Us
                </Button>
              </div>
            </div>

            {/* User Information Card - Mobile Optimized */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-4 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Account Information</h3>
                <p className="mt-1 text-sm text-gray-500">Your account details and preferences</p>
              </div>
              
              <div className="divide-y divide-gray-200">
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-all">{user.email}</dd>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Account Type</dt>
                    <dd className="mt-1 text-sm text-gray-900 capitalize">
                      {profile?.role || "client"}
                    </dd>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    profile?.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                    profile?.role === 'staff' ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {profile?.role || "client"}
                  </span>
                </div>
                
                <div className="px-4 py-3 flex justify-between items-center">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Login</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {new Date(user.last_sign_in_at || Date.now()).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </dd>
                  </div>
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Recent Activity Placeholder */}
            <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-4 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
                <p className="mt-1 text-sm text-gray-500">Your latest bookings and updates</p>
              </div>
              <div className="px-4 py-8 text-center">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-gray-500 text-sm">No recent activity to display</p>
                <p className="text-gray-400 text-xs mt-1">Book your first service to get started</p>
              </div>
            </div>
          </div>
        </Container>
      </main>
    </MobileLayout>
  );
}
