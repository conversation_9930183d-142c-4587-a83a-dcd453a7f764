import { json, redirect } from "@remix-run/node";
import { useLoaderData, useOutletContext } from "@remix-run/react";
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";
import { useState, useEffect } from "react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";

// Types for our data
type Property = {
  id: string;
  name: string;
  address: string;
};

type Booking = {
  id: string;
  service_type: string;
  preferred_date: string;
  preferred_time_slot: string | null;
  urgency: string;
  notes: string | null;
  status: string;
  created_at: string;
  properties: Property;
};

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  // Check authentication
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return redirect("/login");
  }

  // Get user profile and check role
  let { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", session.user.id)
    .single();

  // If no profile exists, create a default profile object for this session
  // Note: We'll handle profile creation through the auth trigger or manually later
  if (profileError || !profile) {
    console.log('No profile found for user, using default client profile');
    profile = {
      id: session.user.id,
      email: session.user.email,
      role: 'client',
      name: session.user.email?.split('@')[0] || 'User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Allow access for clients and admins (admins can view client dashboard too)
  // Default to 'client' role if no profile exists
  const userRole = profile?.role || 'client';

  // For now, allow all authenticated users to access the dashboard
  // In production, you might want to be more restrictive
  console.log(`User ${session.user.email} accessing dashboard with role: ${userRole}`);

  // Fetch user's bookings with property information
  const { data: bookings, error: bookingsError } = await supabase
    .from('bookings')
    .select(`
      *,
      properties (
        id,
        name,
        address
      )
    `)
    .eq('user_id', session.user.id)
    .order('created_at', { ascending: false });

  if (bookingsError) {
    console.error('Error fetching bookings:', bookingsError);
  }

  // Separate active and past bookings
  const activeBookings = (bookings || []).filter(booking =>
    booking.status === 'pending' || booking.status === 'confirmed'
  );

  const pastBookings = (bookings || []).filter(booking =>
    booking.status === 'completed' || booking.status === 'cancelled'
  );

  return json({
    user: session.user,
    profile,
    activeBookings: activeBookings as Booking[],
    pastBookings: pastBookings as Booking[],
    totalBookings: bookings?.length || 0
  }, { headers: response.headers });
}

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return redirect("/login");
  }

  const formData = await request.formData();
  const action = formData.get('action') as string;
  const bookingId = formData.get('bookingId') as string;

  if (action === 'cancel' && bookingId) {
    // Cancel booking - only allow if status is pending
    const { error } = await supabase
      .from('bookings')
      .update({ status: 'cancelled' })
      .eq('id', bookingId)
      .eq('user_id', session.user.id)
      .eq('status', 'pending'); // Only allow cancelling pending bookings

    if (error) {
      return json({ error: 'Failed to cancel booking' }, { status: 500 });
    }

    return json({ success: true });
  }

  return json({ error: 'Invalid action' }, { status: 400 });
}

export default function Dashboard() {
  const { supabase } = useOutletContext<{ supabase: any }>();
  const { user, profile, activeBookings, pastBookings, totalBookings } = useLoaderData<typeof loader>();
  const [showPastBookings, setShowPastBookings] = useState(false);
  const [cancellingBookingId, setCancellingBookingId] = useState<string | null>(null);

  // Check authentication status on client-side
  useEffect(() => {
    if (!supabase) return;

    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data.session) {
        window.location.href = "/login";
      }
    };

    checkSession();
  }, [supabase]);

  // Handle booking cancellation
  const handleCancelBooking = async (bookingId: string) => {
    setCancellingBookingId(bookingId);

    try {
      const formData = new FormData();
      formData.append('action', 'cancel');
      formData.append('bookingId', bookingId);

      const response = await fetch('/dashboard', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        alert('Failed to cancel booking. Please try again.');
      }
    } catch (error) {
      alert('Failed to cancel booking. Please try again.');
    } finally {
      setCancellingBookingId(null);
    }
  };

  // Get user's first name from email or profile
  const getFirstName = () => {
    if (profile?.name) {
      return profile.name.split(' ')[0];
    }
    return user.email?.split('@')[0] || 'there';
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusStyles = (status: string) => {
      switch (status.toLowerCase()) {
        case 'pending':
          return 'bg-yellow-100 text-yellow-800';
        case 'confirmed':
          return 'bg-blue-100 text-blue-800';
        case 'completed':
          return 'bg-green-100 text-green-800';
        case 'cancelled':
          return 'bg-red-100 text-red-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyles(status)}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  // Show loading state if supabase is not initialized yet
  if (!supabase || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-pulse text-center p-4">
          <div className="w-8 h-8 bg-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-gray-700">Loading Dashboard...</h2>
        </div>
      </div>
    );
  }

  return (
    <MobileLayout user={user ? { id: user.id, email: user.email || '', role: profile?.role } : null}>
      {/* Header with personalized greeting */}
      <header className="bg-white shadow-sm sticky top-16 z-30">
        <Container>
          <div className="py-4 px-4">
            <div className="mb-2">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                Hello, {getFirstName()}! 👋
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                {activeBookings.length > 0
                  ? `You have ${activeBookings.length} active booking${activeBookings.length === 1 ? '' : 's'}`
                  : 'No active bookings at the moment'
                }
              </p>
            </div>
          </div>
        </Container>
      </header>

      <main className="bg-gray-50 min-h-[calc(100vh-8rem)]">
        <Container>
          <div className="py-4 px-4 space-y-6">

            {/* Quick Actions */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="primary"
                  className="h-20 flex-col text-sm"
                  onClick={() => window.location.href = "/book"}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Book a Service
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex-col text-sm"
                  onClick={() => window.location.href = "/properties"}
                >
                  <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Manage Properties
                </Button>
              </div>
            </div>

            {/* Active Bookings List */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Active Bookings</h2>
              {activeBookings.length > 0 ? (
                <div className="space-y-3">
                  {activeBookings.map((booking) => (
                    <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-1">
                            <h3 className="text-base font-medium text-gray-900 mb-1">
                              {booking.service_type}
                            </h3>
                            <p className="text-sm text-gray-600 mb-2">
                              📍 {booking.properties.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {booking.properties.address}
                            </p>
                          </div>
                          <StatusBadge status={booking.status} />
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                          <div>
                            <span className="text-gray-500">Date:</span>
                            <p className="font-medium">
                              {new Date(booking.preferred_date).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </p>
                          </div>
                          <div>
                            <span className="text-gray-500">Time:</span>
                            <p className="font-medium">
                              {booking.preferred_time_slot || 'Flexible'}
                            </p>
                          </div>
                        </div>

                        {booking.urgency === 'emergency' && (
                          <div className="mb-3">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              🚨 Emergency Service
                            </span>
                          </div>
                        )}

                        {booking.notes && (
                          <div className="mb-3">
                            <p className="text-sm text-gray-600">
                              <span className="font-medium">Notes:</span> {booking.notes}
                            </p>
                          </div>
                        )}

                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 text-xs"
                            onClick={() => {
                              // TODO: Implement view details modal or navigate to details page
                              alert('View details functionality coming soon!');
                            }}
                          >
                            View Details
                          </Button>
                          {booking.status === 'pending' && (
                            <Button
                              variant="danger"
                              size="sm"
                              className="flex-1 text-xs"
                              disabled={cancellingBookingId === booking.id}
                              onClick={() => handleCancelBooking(booking.id)}
                            >
                              {cancellingBookingId === booking.id ? 'Cancelling...' : 'Cancel Booking'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Bookings</h3>
                  <p className="text-gray-500 text-sm mb-4">You don't have any active service bookings at the moment.</p>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => window.location.href = "/book"}
                  >
                    Book Your First Service
                  </Button>
                </div>
              )}
            </div>

            {/* Past Bookings Section - Collapsible */}
            {pastBookings.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-semibold text-gray-900">Past Bookings</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPastBookings(!showPastBookings)}
                    className="text-sm"
                  >
                    {showPastBookings ? 'Hide' : 'Show'} ({pastBookings.length})
                    <svg
                      className={`w-4 h-4 ml-1 transition-transform ${showPastBookings ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </Button>
                </div>

                {showPastBookings && (
                  <div className="space-y-3">
                    {pastBookings.map((booking) => (
                      <div key={booking.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex-1">
                              <h3 className="text-base font-medium text-gray-900 mb-1">
                                {booking.service_type}
                              </h3>
                              <p className="text-sm text-gray-600">
                                📍 {booking.properties.name}
                              </p>
                            </div>
                            <StatusBadge status={booking.status} />
                          </div>

                          <div className="flex justify-between items-center text-sm text-gray-500 mb-3">
                            <span>
                              {new Date(booking.preferred_date).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </span>
                            <span>
                              {new Date(booking.created_at).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric'
                              })}
                            </span>
                          </div>

                          {booking.status === 'completed' && (
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1 text-xs"
                                onClick={() => {
                                  // Pre-fill booking form with previous data
                                  const params = new URLSearchParams({
                                    serviceType: booking.service_type,
                                    propertyId: booking.properties.id,
                                    urgency: booking.urgency,
                                    ...(booking.notes && { notes: booking.notes })
                                  });
                                  window.location.href = `/book?${params.toString()}`;
                                }}
                              >
                                Re-book Service
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Summary Stats */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-3">Account Summary</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{totalBookings}</div>
                  <div className="text-xs text-gray-500">Total Bookings</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{pastBookings.filter(b => b.status === 'completed').length}</div>
                  <div className="text-xs text-gray-500">Completed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{activeBookings.length}</div>
                  <div className="text-xs text-gray-500">Active</div>
                </div>
              </div>
            </div>

            {/* Footer Actions */}
            <div className="text-center pt-4 pb-8">
              <p className="text-sm text-gray-500 mb-4">
                Need help? Contact our support team.
              </p>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.href = "/contact"}
                  className="w-full"
                >
                  Contact Support
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={async () => {
                    await supabase.auth.signOut();
                    window.location.href = "/";
                  }}
                  className="w-full text-gray-500"
                >
                  Sign Out
                </Button>
              </div>
            </div>
          </div>
        </Container>
      </main>
    </MobileLayout>
  );
}
