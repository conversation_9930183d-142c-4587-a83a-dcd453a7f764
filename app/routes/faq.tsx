import type { MetaFunction } from "@remix-run/node";
import { useState } from "react";
import { Container } from "~/components/ui/Container";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { ButtonLink } from "~/components/ui/Button";

export const meta: MetaFunction = () => {
  return [
    { title: "FAQ - Sia Moon Property Care" },
    { name: "description", content: "Frequently asked questions about Sia Moon Property Care services, pricing, and booking process." },
  ];
};

const faqCategories = [
  {
    id: "general",
    title: "General Questions",
    faqs: [
      {
        question: "What areas do you serve?",
        answer: "We provide property care services island-wide, including the North Shore, South Coast, and Central Region. If you're unsure whether we serve your area, please contact us to confirm availability."
      },
      {
        question: "Are you licensed and insured?",
        answer: "Yes, Sia Moon Property Care is fully licensed and insured. All our team members are background-checked professionals with appropriate certifications for their specialties."
      },
      {
        question: "Do you offer emergency services?",
        answer: "Yes, we provide 24/7 emergency response for urgent issues like water leaks, storm damage, security breaches, and power outages. Emergency rates apply for after-hours services."
      },
      {
        question: "How do I know the work was completed?",
        answer: "After every service, we provide detailed photo reports showing before and after shots, along with notes about the work completed and any recommendations for future maintenance."
      }
    ]
  },
  {
    id: "booking",
    title: "Booking & Scheduling",
    faqs: [
      {
        question: "How do I book a service?",
        answer: "You can book services through our online booking system, by calling us, or via WhatsApp. Simply select your service type, preferred date and time, and provide property details."
      },
      {
        question: "How far in advance should I book?",
        answer: "For regular services, we recommend booking at least 48 hours in advance. For emergency services, we respond immediately. During peak seasons, earlier booking ensures your preferred time slot."
      },
      {
        question: "Can I schedule recurring services?",
        answer: "Absolutely! We offer weekly, bi-weekly, and monthly recurring services for cleaning, pool maintenance, and property check-ins. Recurring customers receive priority scheduling and discounted rates."
      },
      {
        question: "What if I need to reschedule?",
        answer: "You can reschedule services up to 24 hours before the appointment without any fees. For cancellations with less than 24 hours notice, a cancellation fee may apply."
      }
    ]
  },
  {
    id: "services",
    title: "Services & Pricing",
    faqs: [
      {
        question: "What's included in property cleaning?",
        answer: "Our cleaning service includes all interior spaces, bathrooms, kitchen, living areas, bedrooms, and basic exterior areas like patios. We use eco-friendly products and can customize the service based on your specific needs."
      },
      {
        question: "Do you provide your own equipment and supplies?",
        answer: "Yes, we bring all necessary equipment, tools, and eco-friendly cleaning supplies. For pool maintenance, we use professional-grade chemicals and equipment. You don't need to provide anything."
      },
      {
        question: "How is pricing determined?",
        answer: "Pricing is based on property size, service type, frequency, and specific requirements. We offer transparent, upfront pricing with no hidden fees. Contact us for a personalized quote."
      },
      {
        question: "Do you offer package deals?",
        answer: "Yes, we offer discounted package deals for multiple services and recurring bookings. Property owners who book multiple services (cleaning + pool + maintenance) receive special pricing."
      }
    ]
  },
  {
    id: "property",
    title: "Property Access & Security",
    faqs: [
      {
        question: "How do you access my property?",
        answer: "We coordinate property access through lockboxes, property managers, or scheduled key handovers. We maintain strict security protocols and document all property access for your peace of mind."
      },
      {
        question: "What if there's an issue with my property?",
        answer: "If we discover any issues during service (damage, security concerns, maintenance needs), we immediately contact you with photos and recommendations. We can coordinate emergency repairs if needed."
      },
      {
        question: "Can you work while guests are present?",
        answer: "Yes, we can work around guest schedules. For rental properties, we coordinate with your booking calendar to ensure services are completed between guest stays or at convenient times."
      },
      {
        question: "Do you provide property monitoring?",
        answer: "Our regular check-in services include basic property monitoring - checking for damage, security issues, and maintenance needs. We provide detailed reports after each visit."
      }
    ]
  },
  {
    id: "payment",
    title: "Payment & Billing",
    faqs: [
      {
        question: "What payment methods do you accept?",
        answer: "We accept cash, bank transfers, credit cards, and digital payments. For recurring services, we can set up automatic billing for your convenience."
      },
      {
        question: "When is payment due?",
        answer: "Payment is typically due upon service completion. For large projects or recurring services, we can arrange alternative payment schedules. Emergency services require immediate payment."
      },
      {
        question: "Do you provide invoices?",
        answer: "Yes, we provide detailed invoices for all services, including photos and service notes. Invoices can be emailed or provided in hard copy as preferred."
      },
      {
        question: "What if I'm not satisfied with the service?",
        answer: "Your satisfaction is our priority. If you're not completely satisfied, contact us within 24 hours and we'll return to address any issues at no additional charge."
      }
    ]
  }
];

interface FAQItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}

function FAQItem({ question, answer, isOpen, onToggle }: FAQItemProps) {
  return (
    <div className="border border-gray-200 rounded-lg">
      <button
        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
        onClick={onToggle}
      >
        <span className="font-medium text-gray-900">{question}</span>
        <svg
          className={`w-5 h-5 text-gray-500 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      {isOpen && (
        <div className="px-6 pb-4">
          <p className="text-gray-600 leading-relaxed">{answer}</p>
        </div>
      )}
    </div>
  );
}

export default function FAQ() {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [activeCategory, setActiveCategory] = useState("general");

  const toggleItem = (categoryId: string, questionIndex: number) => {
    const itemId = `${categoryId}-${questionIndex}`;
    setOpenItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const activeData = faqCategories.find(cat => cat.id === activeCategory);

  return (
    <MobileLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-12 px-4">
        <Container>
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h1>
            <p className="text-lg md:text-xl opacity-90">
              Find answers to common questions about our property care services
            </p>
          </div>
        </Container>
      </section>

      <div className="py-12 px-4">
        <Container>
          <div className="max-w-4xl mx-auto">
            {/* Category Navigation */}
            <div className="mb-8">
              <div className="flex flex-wrap gap-2 justify-center">
                {faqCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      activeCategory === category.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {category.title}
                  </button>
                ))}
              </div>
            </div>

            {/* FAQ Items */}
            {activeData && (
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                  {activeData.title}
                </h2>
                {activeData.faqs.map((faq, index) => (
                  <FAQItem
                    key={index}
                    question={faq.question}
                    answer={faq.answer}
                    isOpen={openItems.includes(`${activeCategory}-${index}`)}
                    onToggle={() => toggleItem(activeCategory, index)}
                  />
                ))}
              </div>
            )}

            {/* Still Have Questions */}
            <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Still have questions?
              </h3>
              <p className="text-gray-600 mb-6">
                Can't find the answer you're looking for? Our friendly team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <ButtonLink 
                  to="/contact" 
                  variant="primary" 
                  size="lg"
                >
                  Contact Us
                </ButtonLink>
                <a
                  href="https://wa.me/1234567890?text=Hi%20Sia%20Moon%20Property%20Care,%20I%20have%20a%20question%20about%20your%20services."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  📱 WhatsApp Us
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div className="mt-12 grid md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Book a Service</h4>
                <p className="text-gray-600 text-sm mb-4">Ready to schedule your property care service?</p>
                <ButtonLink to="/book" variant="outline" size="sm">
                  Book Now
                </ButtonLink>
              </div>

              <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Our Services</h4>
                <p className="text-gray-600 text-sm mb-4">Learn more about all our property care services</p>
                <ButtonLink to="/services" variant="outline" size="sm">
                  View Services
                </ButtonLink>
              </div>

              <div className="text-center p-6 bg-white border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">About Us</h4>
                <p className="text-gray-600 text-sm mb-4">Learn more about our team and values</p>
                <ButtonLink to="/about" variant="outline" size="sm">
                  About Us
                </ButtonLink>
              </div>
            </div>
          </div>
        </Container>
      </div>
    </MobileLayout>
  );
}
