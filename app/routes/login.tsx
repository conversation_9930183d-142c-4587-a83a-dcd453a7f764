import { useState, useEffect } from "react";
import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useOutletContext, useSearchParams } from "@remix-run/react";
import type { ActionFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";
// import { Container } from "~/components/ui/Container"; // TODO: Use Container when needed
import { Button } from "~/components/ui/Button";

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const action = formData.get("action") as string;
  
  if (action === "login") {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      return json({ error: error.message });
    }
    
    return redirect("/dashboard", {
      headers: response.headers,
    });
  } else {
    const { error } = await supabase.auth.signUp({
      email,
      password,
    });
    
    if (error) {
      return json({ error: error.message });
    }
    
    return json({ success: "Check your email for the confirmation link" });
  }
}

export default function Login() {
  const { supabase } = useOutletContext<{ supabase: any }>();
  const actionData = useActionData<typeof action>();
  const [searchParams] = useSearchParams();
  const [isLogin, setIsLogin] = useState(true);

  // Check if user should start in signup mode
  useEffect(() => {
    const mode = searchParams.get('mode');
    if (mode === 'signup') {
      setIsLogin(false);
    }
  }, [searchParams]);
  
  // Show loading state if supabase is not initialized yet
  if (!supabase) {
    return (
      <MobileLayout hideNavBar hideFooter>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <div className="animate-pulse text-center">
            <div className="w-8 h-8 bg-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <h2 className="text-xl font-bold text-gray-700">Loading...</h2>
          </div>
        </div>
      </MobileLayout>
    );
  }
  
  return (
    <MobileLayout hideNavBar hideFooter>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-8">
        <div className="max-w-md w-full space-y-6">
          {/* Logo/Brand */}
          <div className="text-center">
            <h1 className="text-2xl font-bold text-blue-600 mb-2">Sia Moon Property Care</h1>
            <h2 className="text-xl font-bold text-gray-900">
              {isLogin ? "Welcome Back" : "Create Account"}
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {isLogin ? "Sign in to manage your property services" : "Join us to book property care services"}
            </p>
          </div>
          
          {/* Toggle Login/Signup */}
          <div className="bg-gray-100 p-1 rounded-lg flex">
            <button
              type="button"
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                isLogin 
                  ? "bg-white text-gray-900 shadow-sm" 
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setIsLogin(true)}
            >
              Sign In
            </button>
            <button
              type="button"
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                !isLogin 
                  ? "bg-white text-gray-900 shadow-sm" 
                  : "text-gray-500 hover:text-gray-700"
              }`}
              onClick={() => setIsLogin(false)}
            >
              Sign Up
            </button>
          </div>
          
          {/* Error Message */}
          {actionData && 'error' in actionData && actionData.error && (
            <div className="rounded-lg bg-red-50 border border-red-200 p-4">
              <div className="flex">
                <svg className="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-1 text-sm text-red-700">
                    <p>{actionData.error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Success Message */}
          {actionData && 'success' in actionData && actionData.success && (
            <div className="rounded-lg bg-green-50 border border-green-200 p-4">
              <div className="flex">
                <svg className="w-5 h-5 text-green-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">Success</h3>
                  <div className="mt-1 text-sm text-green-700">
                    <p>{actionData.success}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Form */}
          <Form method="post" className="space-y-4">
            <input type="hidden" name="action" value={isLogin ? "login" : "signup"} />
            
            <div className="space-y-4">
              <div>
                <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  placeholder="Enter your email"
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete={isLogin ? "current-password" : "new-password"}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                  placeholder={isLogin ? "Enter your password" : "Create a password"}
                />
              </div>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="lg"
              fullWidth
              className="mt-6"
            >
              {isLogin ? "Sign In" : "Create Account"}
            </Button>
          </Form>
          
          {/* WhatsApp Contact */}
          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-3">Need help getting started?</p>
            <Button
              variant="outline"
              size="md"
              fullWidth
              onClick={() => window.open('https://wa.me/**********', '_blank')}
            >
              📱 Contact us on WhatsApp
            </Button>
          </div>
        </div>
      </div>
    </MobileLayout>
  );
}
