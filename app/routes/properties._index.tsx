import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useNavigation, Form } from "@remix-run/react";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { createServerSupabase } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Modal } from "~/components/ui/Modal";
import { useState, useEffect } from "react";

export const meta = () => {
  return [
    { title: "Manage Properties - Sia Moon Property Care" },
    { name: "description", content: "Manage your properties with Sia Moon Property Care" },
  ];
};

// Property type based on database schema
type Property = {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  address: string;
  description: string | null;
  bedrooms: number;
  bathrooms: number;
  square_meters: number;
  image_url: string | null;
  user_id: string;
};

// Action data types
type ActionData =
  | { success: string; error?: never; property?: Property }
  | { error: string; success?: never; property?: never }
  | undefined;

export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  // Check authentication
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return redirect("/login");
  }

  // Get user profile with flexible handling
  let { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', session.user.id)
    .single();

  if (profileError || !profile) {
    console.log('No profile found for user, using default client profile for properties');
    profile = {
      id: session.user.id,
      email: session.user.email,
      role: 'client',
      name: session.user.email?.split('@')[0] || 'User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Fetch user's properties
  const { data: properties, error: propertiesError } = await supabase
    .from('properties')
    .select('*')
    .eq('user_id', session.user.id)
    .order('created_at', { ascending: false });

  if (propertiesError) {
    console.error('Error fetching properties:', propertiesError);
  }

  return json({
    user: {
      id: session.user.id,
      email: session.user.email || '',
      name: profile?.name || 'Client'
    },
    properties: properties || []
  }, {
    headers: response.headers,
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return redirect("/login");
  }

  const formData = await request.formData();
  const action = formData.get('action') as string;

  try {
    if (action === 'create') {
      const name = formData.get('name') as string;
      const address = formData.get('address') as string;
      const description = formData.get('description') as string;
      const bedrooms = parseInt(formData.get('bedrooms') as string);
      const bathrooms = parseInt(formData.get('bathrooms') as string);
      const square_meters = parseInt(formData.get('square_meters') as string);
      const image_url = formData.get('image_url') as string;

      // Validate required fields
      if (!name || !address || !bedrooms || !bathrooms || !square_meters) {
        return json({ error: 'Please fill in all required fields' }, { status: 400 });
      }

      const { data, error } = await supabase
        .from('properties')
        .insert({
          name,
          address,
          description: description || null,
          bedrooms,
          bathrooms,
          square_meters,
          image_url: image_url || null,
          user_id: session.user.id
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating property:', error);
        return json({ error: 'Failed to create property' }, { status: 500 });
      }

      return json({ success: 'Property created successfully!', property: data });
    }

    if (action === 'update') {
      const id = formData.get('id') as string;
      const name = formData.get('name') as string;
      const address = formData.get('address') as string;
      const description = formData.get('description') as string;
      const bedrooms = parseInt(formData.get('bedrooms') as string);
      const bathrooms = parseInt(formData.get('bathrooms') as string);
      const square_meters = parseInt(formData.get('square_meters') as string);
      const image_url = formData.get('image_url') as string;

      // Validate required fields
      if (!id || !name || !address || !bedrooms || !bathrooms || !square_meters) {
        return json({ error: 'Please fill in all required fields' }, { status: 400 });
      }

      const { data, error } = await supabase
        .from('properties')
        .update({
          name,
          address,
          description: description || null,
          bedrooms,
          bathrooms,
          square_meters,
          image_url: image_url || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', session.user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating property:', error);
        return json({ error: 'Failed to update property' }, { status: 500 });
      }

      return json({ success: 'Property updated successfully!', property: data });
    }

    if (action === 'delete') {
      const id = formData.get('id') as string;

      if (!id) {
        return json({ error: 'Property ID is required' }, { status: 400 });
      }

      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', id)
        .eq('user_id', session.user.id);

      if (error) {
        console.error('Error deleting property:', error);
        return json({ error: 'Failed to delete property' }, { status: 500 });
      }

      return json({ success: 'Property deleted successfully!' });
    }

    return json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Action error:', error);
    return json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
}

export default function Properties() {
  const { user, properties } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>() as ActionData;
  const navigation = useNavigation();

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);

  const isSubmitting = navigation.state === "submitting";

  // Close modals on successful submission
  useEffect(() => {
    if (actionData?.success) {
      setShowAddModal(false);
      setShowEditModal(false);
      setShowDeleteModal(false);
      setSelectedProperty(null);
    }
  }, [actionData]);

  const handleEdit = (property: Property) => {
    setSelectedProperty(property);
    setShowEditModal(true);
  };

  const handleDelete = (property: Property) => {
    setSelectedProperty(property);
    setShowDeleteModal(true);
  };

  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Properties</h1>
            <p className="text-gray-600 mt-1">Manage your property portfolio</p>
          </div>
          <Button
            variant="primary"
            onClick={() => setShowAddModal(true)}
            className="mt-4 sm:mt-0"
          >
            Add New Property
          </Button>
        </div>

        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800 font-medium">{actionData.success}</p>
          </div>
        )}

        {actionData?.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 font-medium">{actionData.error}</p>
          </div>
        )}

        {/* Properties Grid */}
        {properties.length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No properties yet</h3>
            <p className="text-gray-600 mb-6">Add your first property to start managing it with Sia Moon Property Care.</p>
            <Button
              variant="primary"
              onClick={() => setShowAddModal(true)}
            >
              Add Your First Property
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {properties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))}
          </div>
        )}

        {/* Add Property Modal */}
        <PropertyModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Add New Property"
          isSubmitting={isSubmitting}
        />

        {/* Edit Property Modal */}
        <PropertyModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title="Edit Property"
          property={selectedProperty}
          isSubmitting={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          property={selectedProperty}
          isSubmitting={isSubmitting}
        />
      </Container>
    </MobileLayout>
  );
}

// Property Card Component
function PropertyCard({
  property,
  onEdit,
  onDelete
}: {
  property: Property;
  onEdit: (property: Property) => void;
  onDelete: (property: Property) => void;
}) {
  const defaultImage = "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80";

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow duration-300">
      <div className="h-48 overflow-hidden">
        <img
          src={property.image_url || defaultImage}
          alt={property.name}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-lg text-gray-900 mb-1">{property.name}</h3>
        <p className="text-gray-600 text-sm mb-2">{property.address}</p>
        {property.description && (
          <p className="text-gray-500 text-xs mb-3 line-clamp-2">{property.description}</p>
        )}
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <span className="flex items-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            {property.square_meters}m²
          </span>
          <span className="flex items-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z" />
            </svg>
            {property.bedrooms} BD
          </span>
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {property.bathrooms} BA
          </span>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onEdit(property)}
            className="flex-1"
          >
            Edit
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={() => onDelete(property)}
            className="flex-1"
          >
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
}

// Property Modal Component
function PropertyModal({
  isOpen,
  onClose,
  title,
  property,
  isSubmitting
}: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  property?: Property | null;
  isSubmitting: boolean;
}) {
  if (!isOpen) return null;

  const isEdit = !!property;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title}>
      <Form method="post" className="space-y-4">
        <input type="hidden" name="action" value={isEdit ? "update" : "create"} />
        {isEdit && <input type="hidden" name="id" value={property.id} />}

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Property Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            defaultValue={property?.name || ''}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., Downtown Apartment"
          />
        </div>

        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
            Address *
          </label>
          <input
            type="text"
            id="address"
            name="address"
            defaultValue={property?.address || ''}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="e.g., 123 Main St, City, State"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            defaultValue={property?.description || ''}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Optional description of the property"
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <label htmlFor="bedrooms" className="block text-sm font-medium text-gray-700 mb-1">
              Bedrooms *
            </label>
            <input
              type="number"
              id="bedrooms"
              name="bedrooms"
              defaultValue={property?.bedrooms || ''}
              required
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="bathrooms" className="block text-sm font-medium text-gray-700 mb-1">
              Bathrooms *
            </label>
            <input
              type="number"
              id="bathrooms"
              name="bathrooms"
              defaultValue={property?.bathrooms || ''}
              required
              min="0"
              step="0.5"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="square_meters" className="block text-sm font-medium text-gray-700 mb-1">
              Size (m²) *
            </label>
            <input
              type="number"
              id="square_meters"
              name="square_meters"
              defaultValue={property?.square_meters || ''}
              required
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        <div>
          <label htmlFor="image_url" className="block text-sm font-medium text-gray-700 mb-1">
            Image URL
          </label>
          <input
            type="url"
            id="image_url"
            name="image_url"
            defaultValue={property?.image_url || ''}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div className="flex space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Saving...' : (isEdit ? 'Update Property' : 'Create Property')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
}

// Delete Modal Component
function DeleteModal({
  isOpen,
  onClose,
  property,
  isSubmitting
}: {
  isOpen: boolean;
  onClose: () => void;
  property: Property | null;
  isSubmitting: boolean;
}) {
  if (!isOpen || !property) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Delete Property">
      <div className="space-y-4">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Delete "{property.name}"?
          </h3>
          <p className="text-sm text-gray-500 mb-4">
            This action cannot be undone. This will permanently delete the property and all associated data.
          </p>
        </div>

        <Form method="post" className="flex space-x-3">
          <input type="hidden" name="action" value="delete" />
          <input type="hidden" name="id" value={property.id} />

          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="danger"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Deleting...' : 'Delete Property'}
          </Button>
        </Form>
      </div>
    </Modal>
  );
}
