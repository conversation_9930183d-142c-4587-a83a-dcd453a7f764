import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container } from "~/components/ui/Container";
import { ButtonLink } from "~/components/ui/Button";
import { requireAuth } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";

export const meta = () => {
  return [
    { title: "Manage Properties - Sia Moon Property Care" },
    { name: "description", content: "Manage your properties with Sia Moon Property Care" },
  ];
};

// Define the Property type to match database schema
type Property = {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  address: string;
  description: string | null;
  bedrooms: number;
  bathrooms: number;
  square_meters: number;
  user_id: string;
  image_url: string | null;
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { supabase, session } = await requireAuth(request);
  
  const { data: properties, error } = await supabase
    .from("properties")
    .select("*")
    .eq("user_id", session.user.id)
    .order("created_at", { ascending: false });
  
  if (error) {
    console.error("Error fetching properties:", error);
    return json({ properties: [], error: "Failed to load properties" });
  }
  
  return json({ properties: properties as Property[] });
};

export default function PropertiesIndex() {
  const { properties } = useLoaderData<typeof loader>();
  
  return (
    <MobileLayout>
      <Container className="py-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Properties</h1>
            <p className="text-gray-600 mt-1">Manage your property portfolio</p>
          </div>
          <ButtonLink 
            to="/properties/new" 
            variant="primary"
            className="mt-4 sm:mt-0"
          >
            Add New Property
          </ButtonLink>
        </div>
        
        {properties.length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No properties yet</h3>
            <p className="text-gray-600 mb-6">Add your first property to start managing it with Sia Moon Property Care.</p>
            <ButtonLink to="/properties/new" variant="primary">
              Add Your First Property
            </ButtonLink>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {properties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        )}
      </Container>
    </MobileLayout>
  );
}

function PropertyCard({ property }: { property: Property }) {
  const defaultImage = "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80";
  
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow duration-300">
      <div className="h-48 overflow-hidden">
        <img 
          src={property.image_url || defaultImage} 
          alt={property.name} 
          className="w-full h-full object-cover"
        />
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-lg text-gray-900 mb-1">{property.name}</h3>
        <p className="text-gray-600 text-sm mb-2">{property.address}</p>
        <div className="flex items-center text-sm text-gray-500 mb-4">
          <span className="flex items-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            {property.square_meters}m²
          </span>
          <span className="flex items-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            {property.bedrooms} BD
          </span>
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {property.bathrooms} BA
          </span>
        </div>
        <div className="flex space-x-2">
          <ButtonLink 
            to={`/properties/${property.id}`} 
            variant="secondary"
            className="flex-1 text-center"
          >
            View
          </ButtonLink>
          <ButtonLink 
            to={`/properties/${property.id}/edit`} 
            variant="outline"
            className="flex-1 text-center"
          >
            Edit
          </ButtonLink>
        </div>
      </div>
    </div>
  );
}
