import { json, redirect, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form, useNavigation, useOutletContext } from "@remix-run/react";
import { useState, useEffect } from "react";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { Modal } from "~/components/ui/Modal";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { createServerSupabase } from "~/utils/supabase.server";

export const meta = () => {
  return [
    { title: "My Properties - Sia Moon Property Care" },
    { name: "description", content: "Manage your properties in your Sia Moon Property Care account" },
  ];
};

// Property type based on database schema
type Property = {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  address: string;
  description: string | null;
  bedrooms: number;
  bathrooms: number;
  square_meters: number;
  image_url: string | null;
  user_id: string;
};

// Action data types
type ActionData =
  | { success: string; error?: never; property?: Property }
  | { error: string; success?: never; property?: never }
  | undefined;

// Loader function - fetch user's properties and check authentication
export async function loader({ request }: LoaderFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return redirect("/login");
  }

  // Get user profile with enhanced error handling (same pattern as dashboard/booking)
  let { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', session.user.id)
    .single();

  // If no profile exists, create a default profile object for this session
  if (profileError || !profile) {
    console.log('No profile found for user, using default client profile for properties');
    profile = {
      id: session.user.id,
      email: session.user.email,
      role: 'client',
      name: session.user.email?.split('@')[0] || 'User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Allow all authenticated users to manage properties
  const userRole = profile?.role || 'client';
  console.log(`User ${session.user.email} accessing properties page with role: ${userRole}`);

  // Fetch user's properties
  const { data: properties, error: propertiesError } = await supabase
    .from('properties')
    .select('*')
    .eq('user_id', session.user.id)
    .order('created_at', { ascending: false });

  if (propertiesError) {
    console.error('Error fetching properties:', propertiesError);
  }

  return json({
    user: {
      id: session.user.id,
      email: session.user.email || '',
      name: profile?.name || 'Client'
    },
    properties: properties || []
  }, {
    headers: response.headers,
  });
}

// Action function - handle CRUD operations
export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);

  // Check authentication
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return json({ error: "Authentication required" }, { status: 401 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent") as string;

  try {
    switch (intent) {
      case "create": {
        const name = formData.get("name") as string;
        const address = formData.get("address") as string;
        const description = formData.get("description") as string;
        const bedrooms = parseInt(formData.get("bedrooms") as string) || 0;
        const bathrooms = parseInt(formData.get("bathrooms") as string) || 0;
        const square_meters = parseInt(formData.get("square_meters") as string) || 0;
        const image_url = formData.get("image_url") as string;

        // Validate required fields
        if (!name || !address) {
          return json({ error: "Property name and address are required" });
        }

        // Insert property into database
        const { data: property, error } = await supabase
          .from('properties')
          .insert({
            user_id: session.user.id,
            name: name.trim(),
            address: address.trim(),
            description: description?.trim() || null,
            bedrooms,
            bathrooms,
            square_meters,
            image_url: image_url?.trim() || null
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating property:', error);
          return json({ error: "Failed to create property. Please try again." });
        }

        return json({ success: "Property created successfully!", property });
      }

      case "update": {
        const id = formData.get("id") as string;
        const name = formData.get("name") as string;
        const address = formData.get("address") as string;
        const description = formData.get("description") as string;
        const bedrooms = parseInt(formData.get("bedrooms") as string) || 0;
        const bathrooms = parseInt(formData.get("bathrooms") as string) || 0;
        const square_meters = parseInt(formData.get("square_meters") as string) || 0;
        const image_url = formData.get("image_url") as string;

        // Validate required fields
        if (!id || !name || !address) {
          return json({ error: "Property ID, name and address are required" });
        }

        // Update property in database
        const { data: property, error } = await supabase
          .from('properties')
          .update({
            name: name.trim(),
            address: address.trim(),
            description: description?.trim() || null,
            bedrooms,
            bathrooms,
            square_meters,
            image_url: image_url?.trim() || null
          })
          .eq('id', id)
          .eq('user_id', session.user.id) // Ensure user owns the property
          .select()
          .single();

        if (error) {
          console.error('Error updating property:', error);
          return json({ error: "Failed to update property. Please try again." });
        }

        return json({ success: "Property updated successfully!", property });
      }

      case "delete": {
        const id = formData.get("id") as string;

        if (!id) {
          return json({ error: "Property ID is required" });
        }

        // Delete property from database
        const { error } = await supabase
          .from('properties')
          .delete()
          .eq('id', id)
          .eq('user_id', session.user.id); // Ensure user owns the property

        if (error) {
          console.error('Error deleting property:', error);
          return json({ error: "Failed to delete property. Please try again." });
        }

        return json({ success: "Property deleted successfully!" });
      }

      default:
        return json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error('Action error:', error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
}

export default function Properties() {
  const { user, properties } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>() as ActionData;
  const navigation = useNavigation();
  const { supabase } = useOutletContext<{ supabase: any }>();

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);

  // Loading states
  const isSubmitting = navigation.state === "submitting";
  const isLoading = navigation.state === "loading";

  // Check authentication status on client-side
  useEffect(() => {
    if (!supabase) return;

    const checkSession = async () => {
      const { data } = await supabase.auth.getSession();
      if (!data.session) {
        window.location.href = "/login";
      }
    };

    checkSession();
  }, [supabase]);

  // Close modals on successful submission
  useEffect(() => {
    if (actionData?.success) {
      setShowAddModal(false);
      setShowEditModal(false);
      setShowDeleteModal(false);
      setSelectedProperty(null);
    }
  }, [actionData]);

  // Handle edit property
  const handleEdit = (property: Property) => {
    setSelectedProperty(property);
    setShowEditModal(true);
  };

  // Handle delete property
  const handleDelete = (property: Property) => {
    setSelectedProperty(property);
    setShowDeleteModal(true);
  };

  // Truncate text for mobile display
  const truncateText = (text: string | null, maxLength: number = 100) => {
    if (!text) return "";
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  return (
    <MobileLayout user={user}>
      <Container className="py-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">My Properties</h1>
            <p className="text-gray-600">Manage your properties and their details</p>
          </div>
          <Button
            onClick={() => setShowAddModal(true)}
            className="mt-4 sm:mt-0 w-full sm:w-auto"
            size="lg"
          >
            + Add Property
          </Button>
        </div>

        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-green-800 font-medium">{actionData.success}</p>
          </div>
        )}

        {actionData?.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 font-medium">{actionData.error}</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading properties...</span>
          </div>
        )}

        {/* Properties List */}
        {!isLoading && (
          <div className="space-y-4">
            {properties.length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                  🏠
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No properties yet</h3>
                <p className="text-gray-600 mb-6">Add your first property to get started with bookings</p>
                <Button onClick={() => setShowAddModal(true)}>
                  Add Your First Property
                </Button>
              </div>
            ) : (
              properties.map((property) => (
                <div
                  key={property.id}
                  className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col space-y-3">
                    {/* Property Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {property.name}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          📍 {property.address}
                        </p>
                      </div>
                      {property.image_url && (
                        <div className="ml-4 flex-shrink-0">
                          <img
                            src={property.image_url}
                            alt={property.name}
                            className="h-16 w-16 rounded-lg object-cover"
                          />
                        </div>
                      )}
                    </div>

                    {/* Property Details */}
                    {(property.bedrooms > 0 || property.bathrooms > 0 || property.square_meters > 0) && (
                      <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                        {property.bedrooms > 0 && (
                          <span>🛏️ {property.bedrooms} bed{property.bedrooms !== 1 ? 's' : ''}</span>
                        )}
                        {property.bathrooms > 0 && (
                          <span>🚿 {property.bathrooms} bath{property.bathrooms !== 1 ? 's' : ''}</span>
                        )}
                        {property.square_meters > 0 && (
                          <span>📐 {property.square_meters}m²</span>
                        )}
                      </div>
                    )}

                    {/* Description */}
                    {property.description && (
                      <p className="text-sm text-gray-700">
                        {truncateText(property.description)}
                      </p>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(property)}
                        className="flex-1"
                      >
                        ✏️ Edit
                      </Button>
                      <Button
                        variant="danger"
                        size="sm"
                        onClick={() => handleDelete(property)}
                        className="flex-1"
                      >
                        🗑️ Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </Container>

      {/* Add Property Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Property"
        size="lg"
      >
        <Form method="post" className="space-y-4">
          <input type="hidden" name="intent" value="create" />

          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Property Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Villa Sunset"
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
              Address *
            </label>
            <input
              type="text"
              id="address"
              name="address"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., 123 Ocean View Drive"
            />
          </div>

          <div className="grid grid-cols-3 gap-3">
            <div>
              <label htmlFor="bedrooms" className="block text-sm font-medium text-gray-700 mb-1">
                Bedrooms
              </label>
              <input
                type="number"
                id="bedrooms"
                name="bedrooms"
                min="0"
                defaultValue="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label htmlFor="bathrooms" className="block text-sm font-medium text-gray-700 mb-1">
                Bathrooms
              </label>
              <input
                type="number"
                id="bathrooms"
                name="bathrooms"
                min="0"
                defaultValue="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label htmlFor="square_meters" className="block text-sm font-medium text-gray-700 mb-1">
                Size (m²)
              </label>
              <input
                type="number"
                id="square_meters"
                name="square_meters"
                min="0"
                defaultValue="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label htmlFor="image_url" className="block text-sm font-medium text-gray-700 mb-1">
              Photo URL (optional)
            </label>
            <input
              type="url"
              id="image_url"
              name="image_url"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com/photo.jpg"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Notes (optional)
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Any special instructions, key location, or notes about this property..."
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowAddModal(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isSubmitting}
              className="flex-1"
            >
              Add Property
            </Button>
          </div>
        </Form>
      </Modal>

      {/* Edit Property Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Property"
        size="lg"
      >
        {selectedProperty && (
          <Form method="post" className="space-y-4">
            <input type="hidden" name="intent" value="update" />
            <input type="hidden" name="id" value={selectedProperty.id} />

            <div>
              <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                Property Name *
              </label>
              <input
                type="text"
                id="edit-name"
                name="name"
                required
                defaultValue={selectedProperty.name}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label htmlFor="edit-address" className="block text-sm font-medium text-gray-700 mb-1">
                Address *
              </label>
              <input
                type="text"
                id="edit-address"
                name="address"
                required
                defaultValue={selectedProperty.address}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-3 gap-3">
              <div>
                <label htmlFor="edit-bedrooms" className="block text-sm font-medium text-gray-700 mb-1">
                  Bedrooms
                </label>
                <input
                  type="number"
                  id="edit-bedrooms"
                  name="bedrooms"
                  min="0"
                  defaultValue={selectedProperty.bedrooms}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="edit-bathrooms" className="block text-sm font-medium text-gray-700 mb-1">
                  Bathrooms
                </label>
                <input
                  type="number"
                  id="edit-bathrooms"
                  name="bathrooms"
                  min="0"
                  defaultValue={selectedProperty.bathrooms}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="edit-square_meters" className="block text-sm font-medium text-gray-700 mb-1">
                  Size (m²)
                </label>
                <input
                  type="number"
                  id="edit-square_meters"
                  name="square_meters"
                  min="0"
                  defaultValue={selectedProperty.square_meters}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label htmlFor="edit-image_url" className="block text-sm font-medium text-gray-700 mb-1">
                Photo URL (optional)
              </label>
              <input
                type="url"
                id="edit-image_url"
                name="image_url"
                defaultValue={selectedProperty.image_url || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label htmlFor="edit-description" className="block text-sm font-medium text-gray-700 mb-1">
                Notes (optional)
              </label>
              <textarea
                id="edit-description"
                name="description"
                rows={3}
                defaultValue={selectedProperty.description || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowEditModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting}
                className="flex-1"
              >
                Update Property
              </Button>
            </div>
          </Form>
        )}
      </Modal>

      {/* Delete Property Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Property"
        size="sm"
      >
        {selectedProperty && (
          <div className="space-y-4">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <span className="text-red-600 text-xl">⚠️</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Delete "{selectedProperty.name}"?
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                This action cannot be undone. This will permanently delete the property and all associated data.
              </p>
            </div>

            <Form method="post" className="flex space-x-3">
              <input type="hidden" name="intent" value="delete" />
              <input type="hidden" name="id" value={selectedProperty.id} />

              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="danger"
                isLoading={isSubmitting}
                className="flex-1"
              >
                Delete Property
              </Button>
            </Form>
          </div>
        )}
      </Modal>
    </MobileLayout>
  );
}
