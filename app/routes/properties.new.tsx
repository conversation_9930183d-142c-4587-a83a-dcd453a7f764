import { json, redirect, type ActionFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { Container } from "~/components/ui/Container";
import { Button } from "~/components/ui/Button";
import { requireAuth } from "~/utils/supabase.server";
import { MobileLayout } from "~/components/layout/MobileLayout";

export const meta = () => {
  return [
    { title: "Add New Property - Sia Moon Property Care" },
    { name: "description", content: "Add a new property to your Sia Moon Property Care account" },
  ];
};

type ActionData = {
  errors?: {
    name?: string;
    address?: string;
    type?: string;
    bedrooms?: string;
    bathrooms?: string;
    image_url?: string;
    general?: string;
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const { supabase, session } = await requireAuth(request);
  
  const formData = await request.formData();
  const name = formData.get("name") as string;
  const address = formData.get("address") as string;
  const type = formData.get("type") as string;
  const bedrooms = parseInt(formData.get("bedrooms") as string, 10);
  const bathrooms = parseInt(formData.get("bathrooms") as string, 10);
  const image_url = formData.get("image_url") as string || null;
  
  const errors: ActionData["errors"] = {};
  
  if (!name || name.trim() === "") {
    errors.name = "Property name is required";
  }
  
  if (!address || address.trim() === "") {
    errors.address = "Property address is required";
  }
  
  if (!type || type.trim() === "") {
    errors.type = "Property type is required";
  }
  
  if (isNaN(bedrooms) || bedrooms < 0) {
    errors.bedrooms = "Number of bedrooms must be a positive number";
  }
  
  if (isNaN(bathrooms) || bathrooms < 0) {
    errors.bathrooms = "Number of bathrooms must be a positive number";
  }
  
  if (Object.keys(errors).length > 0) {
    return json({ errors });
  }
  
  const { data, error } = await supabase
    .from("properties")
    .insert([
      {
        name,
        address,
        type,
        bedrooms,
        bathrooms,
        image_url,
        user_id: session.user.id,
      },
    ])
    .select();
  
  if (error) {
    console.error("Error creating property:", error);
    return json({ 
      errors: { 
        general: "Failed to create property. Please try again." 
      } 
    });
  }
  
  return redirect("/properties");
};

export default function NewProperty() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const [imageUrl, setImageUrl] = useState("");
  
  // Property types
  const propertyTypes = [
    "Villa",
    "Apartment",
    "House",
    "Condo",
    "Bungalow",
    "Other"
  ];
  
  return (
    <MobileLayout>
      <Container className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Add New Property</h1>
          <p className="text-gray-600 mt-1">Enter the details of your property</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <Form method="post" className="space-y-4">
            {actionData?.errors?.general && (
              <div className="bg-red-50 text-red-700 p-3 rounded-md text-sm">
                {actionData.errors.general}
              </div>
            )}
            
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Property Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="Beach Villa"
                required
              />
              {actionData?.errors?.name && (
                <p className="mt-1 text-sm text-red-600">{actionData.errors.name}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <input
                type="text"
                id="address"
                name="address"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="123 Beach Road, Koh Samui"
                required
              />
              {actionData?.errors?.address && (
                <p className="mt-1 text-sm text-red-600">{actionData.errors.address}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Property Type
              </label>
              <select
                id="type"
                name="type"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
              >
                <option value="">Select a property type</option>
                {propertyTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
              {actionData?.errors?.type && (
                <p className="mt-1 text-sm text-red-600">{actionData.errors.type}</p>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="bedrooms" className="block text-sm font-medium text-gray-700 mb-1">
                  Bedrooms
                </label>
                <input
                  type="number"
                  id="bedrooms"
                  name="bedrooms"
                  min="0"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="3"
                  required
                />
                {actionData?.errors?.bedrooms && (
                  <p className="mt-1 text-sm text-red-600">{actionData.errors.bedrooms}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="bathrooms" className="block text-sm font-medium text-gray-700 mb-1">
                  Bathrooms
                </label>
                <input
                  type="number"
                  id="bathrooms"
                  name="bathrooms"
                  min="0"
                  step="0.5"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="2"
                  required
                />
                {actionData?.errors?.bathrooms && (
                  <p className="mt-1 text-sm text-red-600">{actionData.errors.bathrooms}</p>
                )}
              </div>
            </div>
            
            <div>
              <label htmlFor="image_url" className="block text-sm font-medium text-gray-700 mb-1">
                Property Image URL (Optional)
              </label>
              <input
                type="url"
                id="image_url"
                name="image_url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="https://example.com/image.jpg"
              />
              <p className="mt-1 text-xs text-gray-500">
                Enter a URL to an image of your property. For now, you can use an image hosting service.
              </p>
              {actionData?.errors?.image_url && (
                <p className="mt-1 text-sm text-red-600">{actionData.errors.image_url}</p>
              )}
              
              {imageUrl && (
                <div className="mt-2">
                  <p className="text-sm font-medium text-gray-700 mb-1">Image Preview:</p>
                  <img 
                    src={imageUrl} 
                    alt="Property preview" 
                    className="h-40 w-full object-cover rounded-md"
                    onError={(e) => {
                      e.currentTarget.src = "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80";
                      e.currentTarget.alt = "Default property image (preview URL was invalid)";
                    }}
                  />
                </div>
              )}
            </div>
            
            <div className="flex space-x-4 pt-4">
              <Button
                type="submit"
                variant="primary"
                isLoading={isSubmitting}
                fullWidth
              >
                {isSubmitting ? "Creating..." : "Create Property"}
              </Button>
              <Button
                type="button"
                variant="outline"
                fullWidth
                onClick={() => window.history.back()}
              >
                Cancel
              </Button>
            </div>
          </Form>
        </div>
      </Container>
    </MobileLayout>
  );
}
