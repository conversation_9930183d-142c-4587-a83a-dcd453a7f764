import type { MetaFunction } from "@remix-run/node";
import { Container } from "~/components/ui/Container";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { ButtonLink } from "~/components/ui/Button";

export const meta: MetaFunction = () => {
  return [
    { title: "Our Services - Sia Moon Property Care" },
    { name: "description", content: "Professional property care services including cleaning, pool maintenance, repairs, and emergency check-ins for villa owners." },
  ];
};

const services = [
  {
    id: "cleaning",
    title: "Property Cleaning",
    icon: "🧹",
    description: "Comprehensive cleaning services for villas, apartments, and rental properties",
    features: [
      "Deep cleaning between guests",
      "Regular maintenance cleaning",
      "Move-in/move-out cleaning",
      "Post-construction cleanup",
      "Eco-friendly cleaning products"
    ],
    pricing: "Starting from $75"
  },
  {
    id: "pool",
    title: "Pool Maintenance",
    icon: "🏊‍♂️",
    description: "Complete pool care to keep your pool crystal clear and safe",
    features: [
      "Weekly chemical balancing",
      "Skimming and vacuuming",
      "Filter cleaning and maintenance",
      "Equipment inspection",
      "Emergency pool repairs"
    ],
    pricing: "Starting from $120/month"
  },
  {
    id: "maintenance",
    title: "General Maintenance",
    icon: "🔧",
    description: "Preventive maintenance and repairs to keep your property in top condition",
    features: [
      "Plumbing repairs and maintenance",
      "Electrical work and inspections",
      "HVAC system maintenance",
      "Appliance repairs",
      "Preventive maintenance schedules"
    ],
    pricing: "Starting from $95/hour"
  },
  {
    id: "checkins",
    title: "Property Check-ins",
    icon: "🔍",
    description: "Regular property inspections and emergency check-ins for peace of mind",
    features: [
      "Weekly/monthly property inspections",
      "Storm damage assessments",
      "Security checks",
      "Detailed photo reports",
      "Emergency response coordination"
    ],
    pricing: "Starting from $45/visit"
  },
  {
    id: "landscaping",
    title: "Landscaping & Grounds",
    icon: "🌿",
    description: "Keep your outdoor spaces beautiful and well-maintained",
    features: [
      "Lawn mowing and edging",
      "Garden maintenance",
      "Tree and shrub trimming",
      "Irrigation system maintenance",
      "Seasonal plantings"
    ],
    pricing: "Starting from $85/visit"
  },
  {
    id: "emergency",
    title: "Emergency Services",
    icon: "🚨",
    description: "24/7 emergency response for urgent property issues",
    features: [
      "24/7 emergency hotline",
      "Storm damage response",
      "Water leak emergency repairs",
      "Security breach response",
      "Power outage coordination"
    ],
    pricing: "Emergency rates apply"
  }
];

export default function Services() {
  return (
    <MobileLayout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-12 px-4">
        <Container>
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Our Services</h1>
            <p className="text-lg md:text-xl opacity-90 mb-8">
              Comprehensive property care services designed for island living
            </p>
            <ButtonLink 
              to="/book" 
              variant="secondary" 
              size="lg"
              className="font-semibold"
            >
              Book a Service
            </ButtonLink>
          </div>
        </Container>
      </section>

      {/* Services Grid */}
      <section className="py-12 px-4">
        <Container>
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {services.map((service) => (
                <div key={service.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-2">{service.icon}</div>
                    <h3 className="text-xl font-semibold text-gray-900">{service.title}</h3>
                    <p className="text-gray-600 mt-2">{service.description}</p>
                  </div>
                  
                  <div className="mb-6">
                    <h4 className="font-medium text-gray-900 mb-3">What's Included:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, index) => (
                        <li key={index} className="flex items-start text-sm text-gray-600">
                          <svg className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-blue-600">{service.pricing}</span>
                      <ButtonLink 
                        to="/book" 
                        variant="outline" 
                        size="sm"
                        className="text-sm"
                      >
                        Book Now
                      </ButtonLink>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </section>

      {/* Service Areas */}
      <section className="py-12 px-4 bg-gray-50">
        <Container>
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">Service Areas</h2>
            <p className="text-lg text-gray-600 mb-8">
              We proudly serve property owners across the island with reliable, professional services.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">North Shore</h3>
                <p className="text-gray-600 text-sm">Including all major villa communities and residential areas</p>
              </div>
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">South Coast</h3>
                <p className="text-gray-600 text-sm">Covering beachfront properties and inland developments</p>
              </div>
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-2">Central Region</h3>
                <p className="text-gray-600 text-sm">Serving mountain properties and central communities</p>
              </div>
            </div>
            
            <p className="text-gray-600">
              Don't see your area listed? <a href="/contact" className="text-blue-600 hover:text-blue-800 font-medium">Contact us</a> to discuss service availability.
            </p>
          </div>
        </Container>
      </section>

      {/* How It Works */}
      <section className="py-12 px-4">
        <Container>
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center mb-12">How It Works</h2>
            
            <div className="grid md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Book Online</h3>
                <p className="text-gray-600 text-sm">Choose your service and preferred time slot through our easy booking system</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">We Confirm</h3>
                <p className="text-gray-600 text-sm">Receive confirmation with service details and our team member information</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Service Delivered</h3>
                <p className="text-gray-600 text-sm">Our professional team completes the work to your satisfaction</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">4</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Photo Report</h3>
                <p className="text-gray-600 text-sm">Receive detailed photos and notes about the completed service</p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-12 px-4 bg-blue-600 text-white">
        <Container>
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Book a Service?</h2>
            <p className="text-lg opacity-90 mb-8">
              Get started with professional property care services today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <ButtonLink 
                to="/book" 
                variant="secondary" 
                size="lg"
                className="font-semibold"
              >
                Book Now
              </ButtonLink>
              <a
                href="https://wa.me/1234567890"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                📱 WhatsApp Us
              </a>
            </div>
          </div>
        </Container>
      </section>
    </MobileLayout>
  );
}
