import { redirect } from "@remix-run/node";
import type { LoaderFunctionArgs } from "@remix-run/node";

// Redirect /signup to /login with signup mode since the login page handles both login and signup
export async function loader({ request }: LoaderFunctionArgs) {
  return redirect("/login?mode=signup");
}

// This component should never render since we always redirect
export default function Signup() {
  return null;
}
