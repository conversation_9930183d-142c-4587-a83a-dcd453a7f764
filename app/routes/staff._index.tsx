import { json, redirect } from "@remix-run/node";
import { useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import { useEffect, useState } from "react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Button, ButtonLink } from "~/components/ui/Button";
import { Container } from "~/components/ui/Container";
import { requireAuth } from "~/utils/supabase.server";

// Define types for our data
type StaffMember = {
  id: string;
  name: string;
  email: string;
  role: string;
};

type Property = {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
};

type Booking = {
  id: string;
  service_type: string;
  preferred_date: string;
  preferred_time_slot: string | null;
  status: "pending" | "in_progress" | "completed";
  notes: string | null;
  property_id: string;
  user_id: string;
  assigned_staff_id: string;
  property?: Property;
};

// Server-side loader function
export async function loader({ request }: { request: Request }) {
  const response = new Response();
  
  try {
    // Require authentication and get Supabase client
    const { supabase, session } = await requireAuth(request);
    
    // Get the staff profile for the current user
    const { data: staffMember, error: staffError } = await supabase
      .from("staff")
      .select("*")
      .eq("email", session.user.email)
      .single();
    
    if (staffError || !staffMember) {
      // If no staff record found, redirect to login
      return redirect("/login");
    }
    
    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split("T")[0];
    
    // Fetch today's bookings for this staff member
    const { data: bookings, error: bookingsError } = await supabase
      .from("bookings")
      .select(`
        *,
        property:property_id (
          id,
          name,
          address,
          city,
          country
        )
      `)
      .eq("assigned_staff_id", staffMember.id)
      .eq("preferred_date", today)
      .in("status", ["pending", "in_progress"]);
    
    if (bookingsError) {
      console.error("Error fetching bookings:", bookingsError);
      return json({ 
        staffMember, 
        bookings: [], 
        error: "Failed to load today's jobs" 
      }, { headers: response.headers });
    }
    
    return json({ 
      staffMember, 
      bookings: bookings || [],
      error: null
    }, { headers: response.headers });
  } catch (error) {
    console.error("Error in staff dashboard loader:", error);
    if (error instanceof Response) return error;
    return redirect("/login");
  }
}

// Action function to handle form submissions
export async function action({ request }: { request: Request }) {
  const response = new Response();
  const formData = await request.formData();
  const { supabase } = await requireAuth(request);
  
  const intent = formData.get("intent") as string;
  const bookingId = formData.get("bookingId") as string;
  
  if (intent === "complete") {
    // Update booking status to completed
    const { error } = await supabase
      .from("bookings")
      .update({ status: "completed" })
      .eq("id", bookingId);
    
    if (error) {
      return json({ success: false, error: error.message }, { headers: response.headers });
    }
    
    return json({ success: true }, { headers: response.headers });
  }
  
  // For future implementation: photo upload
  if (intent === "upload-photo") {
    // This would handle photo uploads to Supabase Storage
    // For now, we'll just return a placeholder success response
    return json({ 
      success: true, 
      message: "Photo upload functionality will be implemented soon" 
    }, { headers: response.headers });
  }
  
  return json({ success: false, error: "Unknown action" }, { headers: response.headers });
}

export default function StaffDashboard() {
  const { staffMember, bookings, error } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const [expandedBooking, setExpandedBooking] = useState<string | null>(null);
  const [toast, setToast] = useState<{ show: boolean; message: string; type: "success" | "error" }>({
    show: false,
    message: "",
    type: "success"
  });
  
  // Show toast when action completes
  useEffect(() => {
    if (navigation.state === "idle" && navigation.formData) {
      const intent = (navigation.formData as FormData).get("intent");
      if (intent === "complete") {
        setToast({
          show: true,
          message: "Job marked as complete!",
          type: "success"
        });
        
        // Hide toast after 3 seconds
        setTimeout(() => {
          setToast({ show: false, message: "", type: "success" });
        }, 3000);
      }
    }
  }, [navigation.state, navigation.formData]);
  
  // Handle marking a job as complete
  const handleMarkComplete = (bookingId: string) => {
    const formData = new FormData();
    formData.append("intent", "complete");
    formData.append("bookingId", bookingId);
    submit(formData, { method: "post" });
  };
  
  // Toggle expanded booking details
  const toggleBookingDetails = (bookingId: string) => {
    if (expandedBooking === bookingId) {
      setExpandedBooking(null);
    } else {
      setExpandedBooking(bookingId);
    }
  };
  
  return (
    <MobileLayout>
      <Container className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Welcome, {staffMember.name}
          </h1>
          <p className="text-gray-600 mt-1">
            Here are your jobs for today.
          </p>
        </div>
        
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}
        
        {/* Toast notification */}
        {toast.show && (
          <div className={`fixed top-4 right-4 left-4 z-50 p-4 rounded-md shadow-lg ${
            toast.type === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
          }`}>
            {toast.message}
          </div>
        )}
        
        {/* Today's Jobs */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Today's Jobs</h2>
          
          {bookings.length === 0 ? (
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <p className="text-gray-500">No jobs scheduled for today.</p>
            </div>
          ) : (
            bookings.map((booking: Booking) => (
              <div 
                key={booking.id} 
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
              >
                {/* Job Card Header */}
                <div className="p-4 flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-medium text-gray-900">{booking.service_type}</h3>
                      <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                        booking.status === "pending" 
                          ? "bg-yellow-100 text-yellow-800" 
                          : "bg-blue-100 text-blue-800"
                      }`}>
                        {booking.status === "pending" ? "Pending" : "In Progress"}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {booking.property?.name} - {booking.property?.address}
                    </p>
                    <p className="text-gray-500 text-sm mt-1">
                      {new Date(booking.preferred_date).toLocaleDateString()} 
                      {booking.preferred_time_slot && ` • ${booking.preferred_time_slot}`}
                    </p>
                  </div>
                  
                  <button
                    onClick={() => toggleBookingDetails(booking.id)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      {expandedBooking === booking.id ? (
                        <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                      ) : (
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      )}
                    </svg>
                  </button>
                </div>
                
                {/* Expanded Job Details */}
                {expandedBooking === booking.id && (
                  <div className="px-4 pb-4 pt-2 border-t border-gray-100">
                    {booking.notes && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700">Client Notes:</h4>
                        <p className="text-gray-600 text-sm mt-1">{booking.notes}</p>
                      </div>
                    )}
                    
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700">Property Details:</h4>
                      <p className="text-gray-600 text-sm mt-1">
                        {booking.property?.address}<br />
                        {booking.property?.city}, {booking.property?.country}
                      </p>
                    </div>
                    
                    {/* Photo Upload - Placeholder for future implementation */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700">Photos:</h4>
                      <div className="mt-2 flex flex-col space-y-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          disabled={true}
                          className="text-sm"
                        >
                          Upload Before Photos
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          disabled={true}
                          className="text-sm"
                        >
                          Upload After Photos
                        </Button>
                        <p className="text-xs text-gray-500 italic">
                          Photo upload coming soon
                        </p>
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex justify-end">
                      <Button
                        variant="primary"
                        onClick={() => handleMarkComplete(booking.id)}
                        disabled={navigation.state !== "idle"}
                      >
                        {navigation.state !== "idle" && 
                         navigation.formData?.get("bookingId") === booking.id ? 
                          "Updating..." : "Mark as Complete"}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
        
        {/* Past Jobs Link */}
        <div className="mt-8">
          <ButtonLink
            variant="secondary"
            fullWidth
            to="/staff/history"
          >
            View Past Jobs
          </ButtonLink>
        </div>
      </Container>
    </MobileLayout>
  );
}
