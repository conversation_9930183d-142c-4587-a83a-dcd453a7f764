import { json, redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { MobileLayout } from "~/components/layout/MobileLayout";
import { Container } from "~/components/ui/Container";
import { requireAuth } from "~/utils/supabase.server";

// Define types for our data
// type StaffMember = {
//   id: string;
//   name: string;
//   email: string;
//   role: string;
// }; // TODO: Use when needed

type Property = {
  id: string;
  name: string;
  address: string;
};

type Booking = {
  id: string;
  service_type: string;
  preferred_date: string;
  preferred_time_slot: string | null;
  status: "pending" | "in_progress" | "completed";
  notes: string | null;
  property_id: string;
  user_id: string;
  assigned_staff_id: string;
  property?: Property;
};

// Server-side loader function
export async function loader({ request }: { request: Request }) {
  const response = new Response();
  
  try {
    // Require authentication and get Supabase client
    const { supabase, session } = await requireAuth(request);
    
    // Get the staff profile for the current user
    const { data: staffMember, error: staffError } = await supabase
      .from("staff")
      .select("*")
      .eq("email", session.user.email)
      .single();
    
    if (staffError || !staffMember) {
      // If no staff record found, redirect to login
      return redirect("/login");
    }
    
    // Fetch completed bookings for this staff member
    const { data: bookings, error: bookingsError } = await supabase
      .from("bookings")
      .select(`
        *,
        property:property_id (
          id,
          name,
          address
        )
      `)
      .eq("assigned_staff_id", staffMember.id)
      .eq("status", "completed")
      .order("preferred_date", { ascending: false })
      .limit(20);
    
    if (bookingsError) {
      console.error("Error fetching bookings:", bookingsError);
      return json({ 
        staffMember, 
        bookings: [], 
        error: "Failed to load completed jobs" 
      }, { headers: response.headers });
    }
    
    return json({ 
      staffMember, 
      bookings: bookings || [],
      error: null
    }, { headers: response.headers });
  } catch (error) {
    console.error("Error in staff history loader:", error);
    if (error instanceof Response) return error;
    return redirect("/login");
  }
}

export default function StaffHistory() {
  const { staffMember, bookings, error } = useLoaderData<typeof loader>();
  
  // Group bookings by month
  const bookingsByMonth: Record<string, Booking[]> = {};
  
  bookings.forEach((booking: Booking) => {
    const date = new Date(booking.preferred_date);
    const monthYear = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    
    if (!bookingsByMonth[monthYear]) {
      bookingsByMonth[monthYear] = [];
    }
    
    bookingsByMonth[monthYear].push(booking);
  });
  
  return (
    <MobileLayout>
      <Container className="py-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Job History
          </h1>
          <p className="text-gray-600 mt-1">
            View your completed jobs.
          </p>
        </div>
        
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}
        
        {/* Completed Jobs */}
        {Object.keys(bookingsByMonth).length === 0 ? (
          <div className="bg-gray-50 rounded-lg p-6 text-center">
            <p className="text-gray-500">No completed jobs found.</p>
          </div>
        ) : (
          Object.entries(bookingsByMonth).map(([monthYear, monthBookings]) => (
            <div key={monthYear} className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">{monthYear}</h2>
              
              <div className="space-y-4">
                {monthBookings.map((booking) => (
                  <div 
                    key={booking.id} 
                    className="bg-white rounded-lg shadow-sm border border-gray-200 p-4"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center">
                          <h3 className="font-medium text-gray-900">{booking.service_type}</h3>
                          <span className="ml-2 px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                            Completed
                          </span>
                        </div>
                        <p className="text-gray-600 text-sm mt-1">
                          {booking.property?.name} - {booking.property?.address}
                        </p>
                        <p className="text-gray-500 text-sm mt-1">
                          {new Date(booking.preferred_date).toLocaleDateString()} 
                          {booking.preferred_time_slot && ` • ${booking.preferred_time_slot}`}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
        
        {/* Back button */}
        <div className="mt-8 flex justify-center">
          <a 
            href="/staff" 
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            ← Back to Today's Jobs
          </a>
        </div>
      </Container>
    </MobileLayout>
  );
}
