import { redirect } from "@remix-run/node";
import { Outlet } from "@remix-run/react";
import { requireAuth } from "~/utils/supabase.server";

// This is a layout route that handles authentication for all staff routes
export async function loader({ request }: { request: Request }) {
  const response = new Response();
  
  try {
    // Require authentication
    const { supabase, session } = await requireAuth(request);
    
    // Check if the user is a staff member
    const { data: staffMember, error } = await supabase
      .from("staff")
      .select("*")
      .eq("email", session.user.email)
      .single();
    
    if (error || !staffMember) {
      // If no staff record found, redirect to login
      return redirect("/login");
    }
    
    return new Response(null, { headers: response.headers });
  } catch (error) {
    console.error("Error in staff layout loader:", error);
    if (error instanceof Response) return error;
    return redirect("/login");
  }
}

export default function StaffLayout() {
  return <Outlet />;
}
