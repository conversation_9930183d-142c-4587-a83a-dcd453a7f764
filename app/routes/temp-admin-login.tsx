import { redirect } from "@remix-run/node";
import type { ActionFunctionArgs } from "@remix-run/node";
import { createServerSupabase } from "~/utils/supabase.server";

export async function action({ request }: ActionFunctionArgs) {
  const response = new Response();
  const supabase = createServerSupabase(request, response);
  
  // Create a temporary admin user session for development
  // WARNING: This is for development only and should be removed in production
  
  try {
    // First, try to find an existing admin user
    const { data: existingUsers, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('role', 'admin')
      .limit(1);
    
    if (fetchError) {
      console.error('Error fetching admin users:', fetchError);
    }
    
    let adminUser = existingUsers?.[0];
    
    // If no admin user exists, create one
    if (!adminUser) {
      // Create a temporary admin user in auth.users
      const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'dev123456',
        email_confirm: true,
        user_metadata: {
          role: 'admin',
          name: 'Development Admin'
        }
      });
      
      if (authError) {
        console.error('Error creating admin auth user:', authError);
        throw new Error('Failed to create admin user');
      }
      
      // Create the user profile
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: authUser.user.id,
          email: '<EMAIL>',
          name: 'Development Admin',
          role: 'admin',
          phone: '+1234567890',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (profileError) {
        console.error('Error creating admin profile:', profileError);
        // Continue anyway, we have the auth user
      }
      
      adminUser = profileData || {
        id: authUser.user.id,
        email: '<EMAIL>',
        name: 'Development Admin',
        role: 'admin'
      };
    }
    
    // Sign in as the admin user
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'dev123456'
    });
    
    if (signInError) {
      console.error('Error signing in admin:', signInError);
      throw new Error('Failed to sign in as admin');
    }
    
    // Redirect to admin dashboard
    return redirect("/admin", {
      headers: response.headers,
    });
    
  } catch (error) {
    console.error('Temporary admin login error:', error);
    
    // Fallback: redirect to regular login with error message
    return redirect("/login?error=temp-admin-failed");
  }
}

// Redirect GET requests to login
export async function loader() {
  return redirect("/login");
}
