/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles with modern aesthetics */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-black text-neutral-100;
    @apply font-sans antialiased;
    @apply transition-colors duration-300;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
    min-height: 100vh;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-black tracking-tight;
    text-rendering: optimizeLegibility;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-accent-500 ring-offset-2 ring-offset-black;
  }

  /* Selection styles */
  ::selection {
    @apply bg-accent-500 text-black;
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-500;
  }

  /* Form elements */
  input, textarea, select {
    @apply bg-secondary-800 border-secondary-600 text-neutral-100;
    @apply focus:border-accent-500 focus:ring-accent-500;
  }

  /* Button base styles */
  button {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Link styles */
  a {
    @apply transition-colors duration-200;
  }

  /* Glass morphism utility */
  .glass {
    @apply bg-secondary-900/20 backdrop-blur-md border border-secondary-600/30;
  }

  /* Clean, modern text effects */
  .text-gradient {
    background: linear-gradient(135deg, #FFF02B 0%, #e6d500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-shadow-sharp {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  }

  /* Pulse animation for backgrounds */
  .animate-pulse-soft {
    animation: pulse-soft 4s ease-in-out infinite;
  }

  @keyframes pulse-soft {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.8; }
  }

  /* Safe area handling */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  /* Text rendering optimization */
  .text-render-optimized {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Gradient animations */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
}
