/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* x.ai inspired design system - Clean, minimal, professional */
@layer base {
  html {
    @apply scroll-smooth;
    @apply bg-surface-primary text-text-primary;
  }

  body {
    @apply bg-surface-primary text-text-primary;
    @apply font-sans antialiased;
    @apply transition-colors duration-200;
    /* Clean, minimal background inspired by x.ai */
    background: #000000;
    min-height: 100vh;
  }

  /* x.ai inspired typography - Clean and readable */
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight text-text-primary;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Professional focus styles */
  *:focus {
    @apply outline-none ring-2 ring-accent-500/20 ring-offset-2 ring-offset-surface-primary;
  }

  /* Clean selection styles */
  ::selection {
    @apply bg-accent-500 text-surface-primary;
  }

  /* Minimal scrollbar styling */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-surface-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-secondary rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-accent;
  }

  /* Form elements with x.ai styling */
  input, textarea, select {
    @apply bg-surface-secondary border border-border-primary text-text-primary;
    @apply placeholder-text-muted;
    @apply focus:border-accent-500 focus:ring-2 focus:ring-accent-500/20;
    @apply transition-all duration-200;
  }

  /* Clean button transitions */
  button {
    @apply transition-all duration-200 ease-out;
  }

  /* Professional link styles */
  a {
    @apply transition-colors duration-200;
  }
}

/* x.ai inspired component styles */
@layer components {
  /* Text effects */
  .text-gradient {
    background: linear-gradient(135deg, #FFF02B 0%, #e6d500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #FFF02B; /* Fallback */
  }

  .text-gradient-subtle {
    background: linear-gradient(135deg, #ffffff 0%, #d4d4d4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ffffff; /* Fallback */
  }

  .text-shadow-sharp {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  }

  .text-shadow-soft {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  /* Surface treatments */
  .surface-primary {
    @apply bg-surface-primary;
  }

  .surface-secondary {
    @apply bg-surface-secondary;
  }

  .surface-elevated {
    @apply bg-surface-tertiary border border-border-primary;
  }

  .surface-interactive {
    @apply bg-surface-quaternary hover:bg-interactive-hover transition-colors duration-200;
  }

  /* Button system */
  .btn-base {
    @apply inline-flex items-center justify-center;
    @apply border border-transparent;
    @apply cursor-pointer select-none;
    @apply transition-all duration-200 ease-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-surface-primary;
    @apply font-medium;
    @apply min-h-touch min-w-touch;
  }

  .btn-primary {
    @apply bg-accent-500 text-surface-primary;
    @apply hover:bg-accent-600 active:bg-accent-700;
    @apply shadow-button hover:shadow-elevated;
    @apply focus:ring-accent-500/20;
  }

  .btn-secondary {
    @apply bg-surface-quaternary text-text-primary border-border-primary;
    @apply hover:bg-interactive-hover active:bg-interactive-active;
    @apply shadow-button hover:shadow-elevated;
    @apply focus:ring-border-primary/20;
  }

  .btn-ghost {
    @apply bg-transparent text-text-secondary;
    @apply hover:bg-interactive-hover hover:text-text-primary;
    @apply focus:ring-border-primary/20;
  }

  /* Card system */
  .card-base {
    @apply bg-surface-secondary border border-border-primary;
    @apply rounded-xl shadow-card;
    @apply transition-all duration-200;
  }

  .card-elevated {
    @apply bg-surface-tertiary border border-border-secondary;
    @apply rounded-xl shadow-elevated;
    @apply hover:shadow-floating;
  }

  .card-interactive {
    @apply card-base;
    @apply hover:bg-surface-tertiary hover:border-border-secondary;
    @apply hover:shadow-elevated cursor-pointer;
    @apply transform hover:scale-[1.01] active:scale-[0.99];
  }

  /* Input system */
  .input-base {
    @apply bg-surface-secondary border border-border-primary;
    @apply text-text-primary placeholder-text-muted;
    @apply rounded-lg px-3 py-2;
    @apply focus:outline-none focus:ring-2 focus:ring-accent-500/20 focus:border-accent-500;
    @apply transition-all duration-200;
  }

  /* Safe area handling */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* x.ai inspired utility layer */
@layer utilities {
  /* Text rendering optimization */
  .text-render-optimized {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch optimization */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .overscroll-contain {
    overscroll-behavior: contain;
  }

  /* Mobile-safe viewport units */
  .h-screen-safe {
    height: calc(100vh - env(safe-area-inset-bottom));
  }

  .min-h-screen-safe {
    min-height: calc(100vh - env(safe-area-inset-bottom));
  }

  /* Professional animations - subtle and clean */
  .animate-fade-in-professional {
    animation: fadeInProfessional 0.3s ease-out;
  }

  .animate-slide-up-professional {
    animation: slideUpProfessional 0.4s ease-out;
  }

  .animate-scale-professional {
    animation: scaleProfessional 0.2s ease-out;
  }

  @keyframes fadeInProfessional {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }

  @keyframes slideUpProfessional {
    0% {
      opacity: 0;
      transform: translateY(16px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleProfessional {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in-professional,
    .animate-slide-up-professional,
    .animate-scale-professional,
    .animate-fade-in-up,
    .animate-slide-up,
    .animate-slide-down,
    .animate-slide-in-right,
    .animate-slide-in-left,
    .animate-bounce-subtle,
    .animate-float,
    .animate-scale-in {
      animation: none;
    }

    .transform,
    .hover\\:scale-\\[1\\.01\\],
    .hover\\:scale-\\[1\\.02\\],
    .active\\:scale-\\[0\\.99\\],
    .active\\:scale-\\[0\\.98\\] {
      transform: none !important;
    }
  }
}
