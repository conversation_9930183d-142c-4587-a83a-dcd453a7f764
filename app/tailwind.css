@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base styles with modern aesthetics */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900;
    @apply text-neutral-900 dark:text-neutral-100;
    @apply font-sans antialiased;
    @apply transition-colors duration-300;
  }

  /* Improved focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-neutral-900;
  }

  /* Smooth scrollbar styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-neutral-100 dark:bg-neutral-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 dark:bg-neutral-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400 dark:bg-neutral-500;
  }
}

/* Component layer for reusable patterns */
@layer components {
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 dark:bg-neutral-900/10 backdrop-blur-md border border-white/20 dark:border-neutral-700/20;
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Card with modern styling */
  .card-modern {
    @apply bg-white/80 dark:bg-neutral-900/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 rounded-2xl shadow-soft hover:shadow-medium transition-all duration-300;
  }

  /* Button base styles */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none rounded-xl;
  }

  /* Shimmer loading effect */
  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    content: '';
    animation: shimmer 2.5s linear infinite;
    background-size: 200% 100%;
  }

  /* Mobile touch optimization */
  .touch-target {
    @apply min-h-touch min-w-touch touch-manipulation;
  }

  /* Text rendering optimization */
  .text-render-optimized {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Pulse animation for backgrounds */
  .animate-pulse-soft {
    animation: pulse-soft 4s ease-in-out infinite;
  }

  @keyframes pulse-soft {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 0.8; }
  }
  }

  /* Safe area handling */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Utility layer for custom utilities */
@layer utilities {
  /* Text rendering optimization */
  .text-render-optimized {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Gradient animations */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
}
