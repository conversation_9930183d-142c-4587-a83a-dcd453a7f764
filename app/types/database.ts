export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          created_at: string
          email: string
          name: string | null
          role: string
        }
        Insert: {
          id: string
          created_at?: string
          email: string
          name?: string | null
          role?: string
        }
        Update: {
          id?: string
          created_at?: string
          email?: string
          name?: string | null
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      properties: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          address: string
          description: string | null
          bedrooms: number
          bathrooms: number
          square_meters: number
          image_url: string | null
          user_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          address: string
          description?: string | null
          bedrooms?: number
          bathrooms?: number
          square_meters?: number
          image_url?: string | null
          user_id: string
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          address?: string
          description?: string | null
          bedrooms?: number
          bathrooms?: number
          square_meters?: number
          image_url?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      staff: {
        Row: {
          id: string
          created_at: string
          name: string
          email: string
          phone: string | null
          role: string
          is_active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          email: string
          phone?: string | null
          role?: string
          is_active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          email?: string
          phone?: string | null
          role?: string
          is_active?: boolean
        }
        Relationships: []
      }
      bookings: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          service_type: string
          user_id: string
          property_id: string
          preferred_date: string
          preferred_time_slot: string | null
          status: string
          assigned_staff_id: string | null
          notes: string | null
          price: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          service_type: string
          user_id: string
          property_id: string
          preferred_date: string
          preferred_time_slot?: string | null
          status?: string
          assigned_staff_id?: string | null
          notes?: string | null
          price?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          service_type?: string
          user_id?: string
          property_id?: string
          preferred_date?: string
          preferred_time_slot?: string | null
          status?: string
          assigned_staff_id?: string | null
          notes?: string | null
          price?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "bookings_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_property_id_fkey"
            columns: ["property_id"]
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_assigned_staff_id_fkey"
            columns: ["assigned_staff_id"]
            referencedRelation: "staff"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Property = Database['public']['Tables']['properties']['Row'];
export type NewProperty = Database['public']['Tables']['properties']['Insert'];
export type UpdateProperty = Database['public']['Tables']['properties']['Update'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Staff = Database['public']['Tables']['staff']['Row'];
export type Booking = Database['public']['Tables']['bookings']['Row'];
export type BookingWithRelations = Booking & {
  property: Property;
  profile: Profile;
  staff?: Staff;
};
