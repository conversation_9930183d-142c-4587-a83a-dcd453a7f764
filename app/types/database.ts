export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          created_at: string
          email: string
          name: string | null
          role: string
        }
        Insert: {
          id: string
          created_at?: string
          email: string
          name?: string | null
          role?: string
        }
        Update: {
          id?: string
          created_at?: string
          email?: string
          name?: string | null
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      properties: {
        Row: {
          id: string
          created_at: string
          user_id: string
          name: string
          address: string
          type: string
          bedrooms: number | null
          bathrooms: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          name: string
          address: string
          type: string
          bedrooms?: number | null
          bathrooms?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          name?: string
          address?: string
          type?: string
          bedrooms?: number | null
          bathrooms?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      bookings: {
        Row: {
          id: string
          created_at: string
          user_id: string
          property_id: string
          service_type: string
          preferred_date: string
          time_slot: string
          urgency: string
          notes: string | null
          status: string
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          property_id: string
          service_type: string
          preferred_date: string
          time_slot: string
          urgency: string
          notes?: string | null
          status?: string
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          property_id?: string
          service_type?: string
          preferred_date?: string
          time_slot?: string
          urgency?: string
          notes?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_property_id_fkey"
            columns: ["property_id"]
            referencedRelation: "properties"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
