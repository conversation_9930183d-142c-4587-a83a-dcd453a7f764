export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      bookings: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          service_type: string
          user_id: string
          property_id: string
          preferred_date: string
          preferred_time_slot: string | null
          status: string
          assigned_staff_id: string | null
          notes: string | null
          price: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          service_type: string
          user_id: string
          property_id: string
          preferred_date: string
          preferred_time_slot?: string | null
          status?: string
          assigned_staff_id?: string | null
          notes?: string | null
          price?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          service_type?: string
          user_id?: string
          property_id?: string
          preferred_date?: string
          preferred_time_slot?: string | null
          status?: string
          assigned_staff_id?: string | null
          notes?: string | null
          price?: number | null
        }
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          email: string | null
          role: string | null
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          email?: string | null
          role?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          email?: string | null
          role?: string | null
        }
      }
      properties: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          address: string
          city: string
          country: string
          user_id: string
          property_type: string | null
          bedrooms: number | null
          bathrooms: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          address: string
          city: string
          country: string
          user_id: string
          property_type?: string | null
          bedrooms?: number | null
          bathrooms?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          address?: string
          city?: string
          country?: string
          user_id?: string
          property_type?: string | null
          bedrooms?: number | null
          bathrooms?: number | null
        }
      }
      staff: {
        Row: {
          id: string
          created_at: string
          name: string
          email: string
          phone: string | null
          role: string | null
          is_active: boolean | null
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          email: string
          phone?: string | null
          role?: string | null
          is_active?: boolean | null
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          email?: string
          phone?: string | null
          role?: string | null
          is_active?: boolean | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
