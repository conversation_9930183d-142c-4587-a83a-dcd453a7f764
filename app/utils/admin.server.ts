import { redirect } from "@remix-run/node";
import { createServerSupabase } from "./supabase.server";

/**
 * Middleware to require admin role for accessing protected routes
 * Redirects to unauthorized page if user is not an admin
 */
export async function requireAdmin(request: Request, response: Response) {
  const supabase = createServerSupabase(request, response);
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session) {
    throw redirect("/login?redirectTo=/admin");
  }
  
  // Check if user has admin role
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", session.user.id)
    .single();
  
  if (!profile || profile.role !== "admin") {
    throw redirect("/unauthorized");
  }
  
  return {
    supabase,
    session,
    profile
  };
}

/**
 * Get dashboard summary metrics for admin
 */
export async function getDashboardMetrics(supabase: any) {
  // Get total bookings count
  const { count: totalBookings } = await supabase
    .from("bookings")
    .select("*", { count: "exact", head: true });
  
  // Get pending jobs count
  const { count: pendingJobs } = await supabase
    .from("bookings")
    .select("*", { count: "exact", head: true })
    .eq("status", "pending");
  
  // Get completed jobs count
  const { count: completedJobs } = await supabase
    .from("bookings")
    .select("*", { count: "exact", head: true })
    .eq("status", "completed");
  
  // Get active clients count (distinct user_ids from bookings)
  const { data: activeClients } = await supabase
    .from("bookings")
    .select("user_id")
    .limit(1000); // Limit to avoid excessive data
  
  const uniqueClients = new Set(activeClients?.map((booking: any) => booking.user_id));
  
  return {
    totalBookings: totalBookings || 0,
    pendingJobs: pendingJobs || 0,
    completedJobs: completedJobs || 0,
    activeClients: uniqueClients.size || 0
  };
}

/**
 * Get monthly report data
 */
export async function getMonthlyReportData(supabase: any) {
  const now = new Date();
  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
  const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();
  
  // Get jobs completed this month
  const { count: jobsCompletedThisMonth } = await supabase
    .from("bookings")
    .select("*", { count: "exact", head: true })
    .eq("status", "completed")
    .gte("updated_at", firstDayOfMonth)
    .lte("updated_at", lastDayOfMonth);
  
  // Get revenue summary (sum of prices for completed jobs this month)
  const { data: completedBookings } = await supabase
    .from("bookings")
    .select("price")
    .eq("status", "completed")
    .gte("updated_at", firstDayOfMonth)
    .lte("updated_at", lastDayOfMonth);
  
  const totalRevenue = completedBookings?.reduce((sum: number, booking: any) => {
    return sum + (booking.price || 0);
  }, 0) || 0;
  
  return {
    jobsCompletedThisMonth: jobsCompletedThisMonth || 0,
    totalRevenue
  };
}
