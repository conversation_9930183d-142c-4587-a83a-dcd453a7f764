import { createServerClient } from "@supabase/auth-helpers-remix";
import type { Database } from "~/types/database";
import { redirect } from "@remix-run/node";

export function createServerSupabase(request: Request, response: Response) {
  const supabaseUrl = process.env.SUPABASE_URL || "";
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || "";

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error("Missing Supabase URL or Anon Key in server environment");
  }

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    request,
    response,
  });
}

export async function requireAuth(request: Request, response: Response) {
  const supabase = createServerSupabase(request, response);
  const { data } = await supabase.auth.getSession();

  if (!data.session) {
    throw redirect("/login");
  }

  return {
    supabase,
    session: data.session,
  };
}

export async function getUserRole(request: Request, response: Response) {
  const { supabase, session } = await requireAuth(request, response);
  
  const { data: profile } = await supabase
    .from("profiles")
    .select("role")
    .eq("id", session.user.id)
    .single();

  return profile?.role || "client";
}
