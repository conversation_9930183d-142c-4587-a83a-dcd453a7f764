-- Disable email confirmation for development
-- Run this in your Supabase SQL Editor

-- Update auth configuration to disable email confirmation
UPDATE auth.config 
SET email_confirm_change = false, 
    email_confirm_signup = false
WHERE true;

-- Alternative: If the above doesn't work, you can manually confirm existing users
-- Uncomment the lines below if you have users that need to be confirmed

-- UPDATE auth.users 
-- SET email_confirmed_at = now(), 
--     confirmed_at = now()
-- WHERE email_confirmed_at IS NULL;

-- Check current auth configuration
SELECT * FROM auth.config;

-- Check users that need confirmation
SELECT id, email, email_confirmed_at, confirmed_at 
FROM auth.users 
WHERE email_confirmed_at IS NULL;
