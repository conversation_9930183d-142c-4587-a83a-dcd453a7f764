-- Enable email signups and check current auth configuration
-- Run this in your Supabase SQL Editor

-- Check current auth configuration
SELECT 
    CASE 
        WHEN disable_signup = true THEN 'Signups are DISABLED'
        ELSE 'Signups are ENABLED'
    END as signup_status,
    CASE 
        WHEN email_confirm_signup = true THEN 'Email confirmation REQUIRED'
        ELSE 'Email confirmation NOT required'
    END as email_confirmation_status
FROM auth.config;

-- Enable signups if they are disabled
UPDATE auth.config 
SET disable_signup = false,
    email_confirm_signup = false,
    email_confirm_change = false
WHERE true;

-- Verify the changes
SELECT 
    disable_signup,
    email_confirm_signup,
    email_confirm_change,
    CASE 
        WHEN disable_signup = false THEN 'Signups are now ENABLED ✓'
        ELSE 'Signups are still DISABLED ✗'
    END as final_status
FROM auth.config;
