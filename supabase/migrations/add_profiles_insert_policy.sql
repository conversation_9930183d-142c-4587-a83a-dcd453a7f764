/*
  # Add INSERT policy for profiles table
  
  This migration adds the missing INSERT policy for the profiles table
  to allow users to create their own profiles when they don't exist.
*/

-- Add policy to allow users to insert their own profile
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);
