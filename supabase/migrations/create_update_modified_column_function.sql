/*
  # Create update_modified_column function
  
  1. New Functions
    - `update_modified_column()`
      - A trigger function that automatically updates the updated_at column
      - Used by multiple tables to maintain last-modified timestamps
  
  2. Purpose
    - Provides automatic timestamp management for tables with updated_at columns
    - Ensures consistent timestamp handling across the database
*/

-- Create the function to update the updated_at column
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;