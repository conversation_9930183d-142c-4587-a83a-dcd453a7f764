-- Password Reset Configuration for Supabase
-- Run this in your Supabase SQL Editor to check and configure password reset settings

-- Check current auth configuration
SELECT 
    CASE 
        WHEN disable_signup = true THEN 'Signups are DISABLED'
        ELSE 'Signups are ENABLED'
    END as signup_status,
    CASE 
        WHEN email_confirm_signup = true THEN 'Email confirmation REQUIRED for signup'
        ELSE 'Email confirmation NOT required for signup'
    END as signup_email_confirmation,
    CASE 
        WHEN email_confirm_change = true THEN 'Email confirmation REQUIRED for changes'
        ELSE 'Email confirmation NOT required for changes'
    END as change_email_confirmation
FROM auth.config;

-- Ensure password reset functionality is properly configured
-- Note: Most password reset settings are configured in the Supabase Dashboard
-- under Authentication > Settings > Auth Providers > Email

-- You can also check if there are any custom email templates configured
-- This would be done in the Dashboard under Authentication > Settings > Email Templates

-- For development, you might want to ensure email confirmation is disabled
-- so password resets work smoothly
UPDATE auth.config 
SET email_confirm_signup = false,
    email_confirm_change = false
WHERE true;

-- Verify the changes
SELECT 
    disable_signup,
    email_confirm_signup,
    email_confirm_change,
    'Password reset should work now' as status
FROM auth.config;
