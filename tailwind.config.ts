import type { Config } from "tailwindcss";

export default {
  content: ["./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: [
          '"Inter"',
          '"SF Pro Display"',
          '"Satoshi"',
          "ui-sans-serif",
          "system-ui",
          "sans-serif",
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"',
        ],
      },
      // Mobile-first spacing and sizing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Mobile-optimized breakpoints
      screens: {
        'xs': '360px',    // Small phones
        'sm': '414px',    // Large phones
        'md': '768px',    // Tablets
        'lg': '1024px',   // Laptops
        'xl': '1280px',   // Desktops
        '2xl': '1536px',  // Large desktops
      },
      // Touch-friendly sizing
      minHeight: {
        'touch': '44px',  // Minimum touch target size
        'screen-safe': 'calc(100vh - env(safe-area-inset-bottom))',
      },
      minWidth: {
        'touch': '44px',
      },
      // Dark masculine brand palette
      colors: {
        // Brand colors - Black, Grey, Yellow
        brand: {
          black: '#000000',
          grey: '#4D4D4D',
          yellow: '#FFF02B',
        },
        // Primary - Black scale for dark masculine theme
        primary: {
          50: '#f8f8f8',
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#c0c0c0',
          400: '#a0a0a0',
          500: '#808080',
          600: '#666666',
          700: '#4D4D4D',
          800: '#333333',
          900: '#1a1a1a',
          950: '#000000',
        },
        // Secondary - Grey scale for depth
        secondary: {
          50: '#fafafa',
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#c0c0c0',
          400: '#a0a0a0',
          500: '#808080',
          600: '#666666',
          700: '#4D4D4D',
          800: '#333333',
          900: '#1a1a1a',
          950: '#0d0d0d',
        },
        // Accent - Yellow scale for highlights
        accent: {
          50: '#fffef0',
          100: '#fffcdb',
          200: '#fff8b8',
          300: '#fff085',
          400: '#ffe651',
          500: '#FFF02B',
          600: '#e6d500',
          700: '#ccbf00',
          800: '#b3a600',
          900: '#998c00',
          950: '#4d4600',
        },
        // Neutral - Dark grays for backgrounds
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
          950: '#000000',
        },
        // Success, warning, error with modern tones
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
      },
      // Dark masculine gradient backgrounds
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'gradient-primary': 'linear-gradient(135deg, var(--tw-gradient-stops))',
        'gradient-hero': 'linear-gradient(135deg, #000000 0%, #4D4D4D 50%, #000000 100%)',
        'gradient-card': 'linear-gradient(145deg, rgba(77,77,77,0.1) 0%, rgba(0,0,0,0.05) 100%)',
        'gradient-dark': 'linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)',
        'gradient-yellow': 'linear-gradient(135deg, #FFF02B 0%, #e6d500 100%)',
        'gradient-grey': 'linear-gradient(135deg, #4D4D4D 0%, #333333 100%)',
        'gradient-black': 'linear-gradient(135deg, #1a1a1a 0%, #000000 100%)',
        'gradient-accent': 'linear-gradient(135deg, #000000 0%, #FFF02B 50%, #000000 100%)',
      },
      // Dark masculine shadows with depth
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3)',
        'large': '0 10px 40px -10px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.4)',
        'glow': '0 0 20px rgba(255, 240, 43, 0.4)',
        'glow-yellow': '0 0 30px rgba(255, 240, 43, 0.6)',
        'glow-grey': '0 0 20px rgba(77, 77, 77, 0.4)',
        'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.3)',
        'inner-dark': 'inset 0 2px 8px 0 rgba(0, 0, 0, 0.5)',
      },
      // Enhanced animations and transitions
      animation: {
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'slide-in-right': 'slideInRight 0.3s ease-out',
        'slide-in-left': 'slideInLeft 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-in',
        'fade-in-up': 'fadeInUp 0.4s ease-out',
        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
        'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'shimmer': 'shimmer 2.5s linear infinite',
      },
      keyframes: {
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(168, 85, 247, 0.2)' },
          '100%': { boxShadow: '0 0 20px rgba(168, 85, 247, 0.6)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
      // Safe area support for mobile devices
      padding: {
        'safe': 'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
      },
      margin: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
      },
    },
  },
  plugins: [
    // Add custom mobile utilities
    function({ addUtilities }: { addUtilities: any }) {
      const newUtilities = {
        '.touch-manipulation': {
          'touch-action': 'manipulation',
        },
        '.overscroll-contain': {
          'overscroll-behavior': 'contain',
        },
        '.overscroll-none': {
          'overscroll-behavior': 'none',
        },
        '.scroll-smooth': {
          'scroll-behavior': 'smooth',
        },
        // Improved text rendering on mobile
        '.text-render-mobile': {
          '-webkit-font-smoothing': 'antialiased',
          '-moz-osx-font-smoothing': 'grayscale',
          'text-rendering': 'optimizeLegibility',
        },
        // Mobile-safe viewport units
        '.h-screen-safe': {
          height: 'calc(100vh - env(safe-area-inset-bottom))',
        },
        '.min-h-screen-safe': {
          'min-height': 'calc(100vh - env(safe-area-inset-bottom))',
        },
      };
      addUtilities(newUtilities);
    },
  ],
} satisfies Config;
