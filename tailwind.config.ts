import type { Config } from "tailwindcss";

export default {
  content: ["./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {
        sans: [
          '"Inter"',
          '"SF Pro Display"',
          '"Satoshi"',
          "ui-sans-serif",
          "system-ui",
          "sans-serif",
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"',
        ],
      },
      // Mobile-first spacing and sizing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Mobile-optimized breakpoints
      screens: {
        'xs': '360px',    // Small phones
        'sm': '414px',    // Large phones
        'md': '768px',    // Tablets
        'lg': '1024px',   // Laptops
        'xl': '1280px',   // Desktops
        '2xl': '1536px',  // Large desktops
      },
      // Touch-friendly sizing
      minHeight: {
        'touch': '44px',  // Minimum touch target size
        'screen-safe': 'calc(100vh - env(safe-area-inset-bottom))',
      },
      minWidth: {
        'touch': '44px',
      },
      // x.ai inspired color system - Deep blacks and high contrast
      colors: {
        // x.ai inspired surface colors - Deep blacks and charcoals for backgrounds
        surface: {
          primary: '#000000',     // Pure black for main backgrounds
          secondary: '#0a0a0a',   // Slightly lighter for cards
          tertiary: '#171717',    // For elevated surfaces
          quaternary: '#262626',  // For interactive elements
          elevated: '#1a1a1a',    // For modals and overlays
        },
        // Text colors with high contrast for readability
        text: {
          primary: '#ffffff',     // Pure white for primary text
          secondary: '#d4d4d4',   // Light grey for secondary text
          tertiary: '#a3a3a3',    // Medium grey for tertiary text
          muted: '#737373',       // Muted grey for disabled/placeholder
          inverse: '#000000',     // Black text for light backgrounds
        },
        // Brand colors - maintaining original brand identity
        brand: {
          black: '#000000',
          grey: '#4D4D4D',
          yellow: '#FFF02B',
        },
        // Accent colors (yellow variations) for CTAs and highlights
        accent: {
          50: '#fefce8',
          100: '#fef9c3',
          200: '#fef08a',
          300: '#fde047',
          400: '#facc15',
          500: '#FFF02B',         // Primary brand yellow
          600: '#e6d500',         // Darker yellow for hover states
          700: '#ca8a04',
          800: '#a16207',
          900: '#854d0e',
          950: '#713f12',
        },
        // Status colors for feedback with x.ai inspired tones
        status: {
          success: '#10b981',     // Green for success states
          warning: '#f59e0b',     // Amber for warnings
          error: '#ef4444',       // Red for errors
          info: '#3b82f6',        // Blue for information
        },
        // Border colors for clean separation
        border: {
          primary: '#262626',     // Primary border color
          secondary: '#404040',   // Secondary border color
          accent: '#FFF02B',      // Accent border color
          muted: '#171717',       // Subtle borders
        },
        // Interactive states
        interactive: {
          hover: '#1a1a1a',       // Hover background
          active: '#262626',      // Active/pressed state
          focus: '#FFF02B',       // Focus ring color
          disabled: '#404040',    // Disabled state
        },
        // Legacy color support for existing components
        primary: {
          50: '#f8f8f8',
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#c0c0c0',
          400: '#a0a0a0',
          500: '#808080',
          600: '#666666',
          700: '#4D4D4D',
          800: '#333333',
          900: '#1a1a1a',
          950: '#000000',
        },
        secondary: {
          50: '#fafafa',
          100: '#f0f0f0',
          200: '#e0e0e0',
          300: '#c0c0c0',
          400: '#a0a0a0',
          500: '#808080',
          600: '#666666',
          700: '#4D4D4D',
          800: '#333333',
          900: '#1a1a1a',
          950: '#0d0d0d',
        },
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
          950: '#000000',
        },
      },
      // x.ai inspired gradient backgrounds - minimal and clean
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        // Clean, minimal gradients inspired by x.ai
        'gradient-primary': 'linear-gradient(180deg, #000000 0%, #0a0a0a 100%)',
        'gradient-hero': 'linear-gradient(180deg, #000000 0%, #171717 50%, #000000 100%)',
        'gradient-card': 'linear-gradient(145deg, rgba(23,23,23,0.8) 0%, rgba(10,10,10,0.9) 100%)',
        'gradient-surface': 'linear-gradient(180deg, #0a0a0a 0%, #000000 100%)',
        'gradient-elevated': 'linear-gradient(145deg, #171717 0%, #0a0a0a 100%)',
        // Accent gradients for CTAs
        'gradient-accent': 'linear-gradient(135deg, #FFF02B 0%, #e6d500 100%)',
        'gradient-accent-hover': 'linear-gradient(135deg, #e6d500 0%, #ccbf00 100%)',
        // Subtle background patterns
        'gradient-mesh': 'radial-gradient(circle at 20% 50%, rgba(255,240,43,0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,240,43,0.03) 0%, transparent 50%)',
      },
      // x.ai inspired shadow system - subtle and professional
      boxShadow: {
        // Basic shadows
        'xs': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        // Custom shadows for specific use cases
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'button': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'elevated': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'floating': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        // Focus and interaction shadows
        'focus': '0 0 0 3px rgba(255, 240, 43, 0.1)',
        'focus-accent': '0 0 0 3px rgba(255, 240, 43, 0.2)',
        // Border-like shadows for dark themes
        'border': '0 0 0 1px rgba(255, 255, 255, 0.05)',
        'border-accent': '0 0 0 1px rgba(255, 240, 43, 0.2)',
        // Inner shadows
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'inner-lg': 'inset 0 4px 8px 0 rgba(0, 0, 0, 0.1)',
      },
      // Enhanced animations and transitions
      animation: {
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'slide-in-right': 'slideInRight 0.3s ease-out',
        'slide-in-left': 'slideInLeft 0.3s ease-out',
        'fade-in': 'fadeIn 0.2s ease-in',
        'fade-in-up': 'fadeInUp 0.4s ease-out',
        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
        'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'float': 'float 3s ease-in-out infinite',
        'scale': 'scale 0.2s ease-out',
        'shimmer': 'shimmer 2.5s linear infinite',
      },
      keyframes: {
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        pulseSoft: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.8' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        scale: {
          '0%': { transform: 'scale(1)' },
          '100%': { transform: 'scale(1.02)' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
      // Safe area support for mobile devices
      padding: {
        'safe': 'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
      },
      margin: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
      },
    },
  },
  plugins: [
    // Add custom mobile utilities
    function({ addUtilities }: { addUtilities: any }) {
      const newUtilities = {
        '.touch-manipulation': {
          'touch-action': 'manipulation',
        },
        '.overscroll-contain': {
          'overscroll-behavior': 'contain',
        },
        '.overscroll-none': {
          'overscroll-behavior': 'none',
        },
        '.scroll-smooth': {
          'scroll-behavior': 'smooth',
        },
        // Improved text rendering on mobile
        '.text-render-mobile': {
          '-webkit-font-smoothing': 'antialiased',
          '-moz-osx-font-smoothing': 'grayscale',
          'text-rendering': 'optimizeLegibility',
        },
        // Mobile-safe viewport units
        '.h-screen-safe': {
          height: 'calc(100vh - env(safe-area-inset-bottom))',
        },
        '.min-h-screen-safe': {
          'min-height': 'calc(100vh - env(safe-area-inset-bottom))',
        },
      };
      addUtilities(newUtilities);
    },
  ],
} satisfies Config;
